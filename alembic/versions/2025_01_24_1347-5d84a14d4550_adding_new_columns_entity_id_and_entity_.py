"""adding new columns entity_id and entity_type in requests history table

Revision ID: 5d84a14d4550
Revises: c28f76f448d0
Create Date: 2025-01-24 13:47:47.201991

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5d84a14d4550'
down_revision: Union[str, None] = 'c28f76f448d0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('requests_history', sa.Column('entity_id', sa.BIGINT, nullable = True))
    op.add_column('requests_history', sa.Column('entity_type', sa.VARCHAR, nullable = True))
    
    op.alter_column('requests_history', 'email_id', nullable=True)
    op.alter_column('requests_history', 'response_accepted_status', default=False)
    

def downgrade() -> None:
    op.drop_column('requests_history', 'entity_id')
    op.drop_column('requests_history', 'entity_type')
    
