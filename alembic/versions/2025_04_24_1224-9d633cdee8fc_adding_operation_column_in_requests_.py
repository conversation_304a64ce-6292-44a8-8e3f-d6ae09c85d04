"""adding operation column in requests_history table

Revision ID: 9d633cdee8fc
Revises: a990654a0b25
Create Date: 2025-04-24 12:24:43.928735

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9d633cdee8fc'
down_revision: Union[str, None] = 'a990654a0b25'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('requests_history', sa.Column('operation', sa.String(), nullable=True))

    op.execute("UPDATE requests_history SET operation = 'email'")

    op.alter_column('requests_history', 'operation', nullable=False)


def downgrade() -> None:
    op.drop_column('requests_history', 'operation')
