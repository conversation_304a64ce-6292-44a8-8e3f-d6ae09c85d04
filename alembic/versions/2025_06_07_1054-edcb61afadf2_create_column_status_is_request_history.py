"""Create column status is request_history

Revision ID: edcb61afadf2
Revises: 9d633cdee8fc
Create Date: 2025-06-07 10:54:27.042995

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'edcb61afadf2'
down_revision: Union[str, None] = '9d633cdee8fc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('requests_history', sa.Column('status', sa.String(), nullable=True))



def downgrade() -> None:
    op.drop_column('requests_history', 'status')

