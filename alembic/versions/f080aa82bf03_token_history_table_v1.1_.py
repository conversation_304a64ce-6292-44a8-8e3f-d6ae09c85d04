"""Token History Table

Revision ID: f080aa82bf03
Revises: 8f5ed53a03b2
Create Date: 2025-01-15 23:15:25.029668

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f080aa82bf03'
down_revision: Union[str, None] = '8f5ed53a03b2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'token_history',
        sa.Column('id', sa.BIGINT, primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.BIGINT, sa.<PERSON>ey("users.id"), nullable=False, primary_key=True),
        sa.Column('tenant_id', sa.BIGINT, nullable=False),
        sa.Column('token_limit', sa.BIGINT, nullable=False),
        sa.Column('token_consumed', sa.BIGINT, nullable=False),
        sa.Column('usage_date', sa.TIMESTAMP, nullable=False),
    )


def downgrade() -> None:
    op.drop_table('token_history')
