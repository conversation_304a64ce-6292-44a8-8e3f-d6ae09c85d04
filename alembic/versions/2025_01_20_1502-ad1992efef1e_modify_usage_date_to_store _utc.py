"""updating  token history table

Revision ID: ad1992efef1e
Revises: 75de305fe506
Create Date: 2025-01-20 15:02:46.668541

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ad1992efef1e'
down_revision: Union[str, None] = '75de305fe506'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Alter the column to ensure UTC timestamp with timezone
    op.alter_column('token_history', 
                    'usage_date',
                    existing_type=sa.TIMESTAMP(),  # Existing type (without timezone)
                    type_=sa.TIMESTAMP(timezone=True),  # New type with timezone
                    nullable=False,
                    server_default=sa.text("CURRENT_TIMESTAMP AT TIME ZONE 'UTC'")
    )

def downgrade() -> None:
    # Revert the column back to original state if needed
    op.alter_column('token_history', 
                    'usage_date',
                    existing_type=sa.TIMESTAMP(timezone=True),
                    type_=sa.TIMESTAMP(),
                    nullable=False
    )