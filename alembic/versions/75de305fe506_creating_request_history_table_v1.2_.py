"""Creating Request  History Table

Revision ID: 75de305fe506
Revises: f080aa82bf03
Create Date: 2025-01-15 23:47:59.790380

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB


# revision identifiers, used by Alembic.
revision: str = '75de305fe506'
down_revision: Union[str, None] = 'f080aa82bf03'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'requests_history',
        sa.Column('id', sa.BIGINT, primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.BIGINT, sa.ForeignKey("users.id"), nullable=False, primary_key=True),
        sa.Column('tenant_id', sa.BIGINT, nullable=False),
        sa.Column('requested_input', JSONB, nullable=False),
        sa.Column('generated_response', JSONB, nullable=False),
        sa.Column('token_consumed', sa.BIGINT, nullable=False),
        sa.Column('request_date', sa.TIMESTAMP, nullable=False),
    )


def downgrade() -> None:
    pass
    op.drop_table('requests_history')
