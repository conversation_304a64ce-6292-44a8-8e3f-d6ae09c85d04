"""changing thread_id column name to email_id in request history table

Revision ID: c28f76f448d0
Revises: 520cf5e558fd
Create Date: 2025-01-24 13:41:03.306500

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c28f76f448d0'
down_revision: Union[str, None] = '520cf5e558fd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column('requests_history', 'thread_id', new_column_name='email_id')


def downgrade() -> None:
    op.alter_column('requests_history', 'email_id', new_column_name='thread_id')
