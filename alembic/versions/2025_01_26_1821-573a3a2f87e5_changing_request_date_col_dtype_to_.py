"""changing request_date col dtype to TIMESTAMP in request_history table

Revision ID: 573a3a2f87e5
Revises: 5d84a14d4550
Create Date: 2025-01-26 18:21:13.747639

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '573a3a2f87e5'
down_revision: Union[str, None] = '5d84a14d4550'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Apply migration: Alter column type to TIMESTAMP."""
    op.alter_column(
        'requests_history',
        'request_date',    
        type_=sa.TIMESTAMP(),     
    )


def downgrade() -> None:
    """Rollback migration: Revert column type to original."""
    op.alter_column(
        'requests_history', 
        'request_date',     
        type_=sa.DATE,
    )