"""adding new columns in request history table

Revision ID: 520cf5e558fd
Revises: ad1992efef1e
Create Date: 2025-01-20 15:10:01.656630

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


revision: str = '520cf5e558fd'
down_revision: Union[str, None] = 'ad1992efef1e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('requests_history', sa.Column('thread_id', sa.BIGINT, nullable=False))
    op.add_column('requests_history', sa.Column('input_token_consumed', sa.BIGINT, nullable=False))
    op.add_column('requests_history', sa.Column('output_token_consumed', sa.BIGINT, nullable=False))
    op.add_column('requests_history', sa.Column('total_consumed', sa.BIGINT, nullable=False))
    op.add_column('requests_history', sa.Column('response_accepted_status', sa.<PERSON>, nullable=False))

    op.alter_column('requests_history', 'request_date',
                    existing_type=sa.TIMESTAMP(),
                    type_=sa.DATE(),
                    nullable=False)

    op.drop_column('requests_history', 'token_consumed')

def downgrade() -> None:
    op.drop_column('requests_history', 'thread_id')
    op.drop_column('requests_history', 'input_token_consumed')
    op.drop_column('requests_history', 'output_token_consumed')
    op.drop_column('requests_history', 'total_consumed')
    op.drop_column('requests_history', 'response_accepted_status')

    op.alter_column('requests_history', 'request_date',
                    existing_type=sa.DATE(),
                    type_=sa.TIMESTAMP(),
                    nullable=False)

    op.add_column('requests_history', sa.Column('token_consumed', sa.BIGINT, nullable=False))
