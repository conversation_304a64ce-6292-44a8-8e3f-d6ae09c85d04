"""Creating User table

Revision ID: 8f5ed53a03b2
Revises: 
Create Date: 2025-01-15 20:16:11.727872

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8f5ed53a03b2'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'users',
        sa.Column('id', sa.BIGINT, primary_key=True),
        sa.Column('name', sa.VARCHAR(512), nullable=False),
        sa.Column('email', sa.VARCHAR(255), nullable=True),
        sa.Column('tenant_id', sa.BIGINT, nullable=False)
    )


def downgrade() -> None:
    op.drop_table('users')

