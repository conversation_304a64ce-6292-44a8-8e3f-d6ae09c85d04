"""changing usage_date col dtype to date in token_history table

Revision ID: a990654a0b25
Revises: 573a3a2f87e5
Create Date: 2025-01-26 22:51:07.879461

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a990654a0b25'
down_revision: Union[str, None] = '573a3a2f87e5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column(
        'token_history',
        'usage_date',    
        type_=sa.Date(),     
    )


def downgrade() -> None:
    op.alter_column(
        'token_history',
        'usage_date',    
        type_=sa.TIMESTAMP(),     
    )
