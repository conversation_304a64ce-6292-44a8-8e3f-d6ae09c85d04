from pydantic import BaseSettings, Field

class Config(BaseSettings):
    # General settings
    environment: str = Field(..., env="ENVIRONMENT")
    server_port: int = Field(..., env="SERVER_PORT")
    secret_key: str = Field(..., env="SECRET_KEY")
    algorithm: str = Field(..., env="ALGORITHM")

    # PostgreSQL settings
    postgres_host: str = Field(..., env="POSTGRES_HOST")
    postgres_port: int = Field(..., env="POSTGRES_PORT")
    postgres_user: str = Field(..., env="POSTGRES_USER")
    postgres_password: str = Field(..., env="POSTGRES_PASSWORD")
    postgres_db: str = Field(..., env="POSTGRES_DB")
    database_url: str = Field(..., env="DATABASE_URL")

    # RabbitMQ settings
    rabbitmq_host: str = Field(..., env="RABBITMQ_HOST")
    rabbitmq_username: str = Field(..., env="RABBITMQ_USERNAME")
    rabbitmq_password: str = Field(..., env="RABBITMQ_PASSWORD")
    rabbitmq_virtual_host: str = Field(..., env="RABBITMQ_VIRTUAL_HOST")

    # Service URLs
    iam_base_path: str = Field(..., env="IAM_BASE_PATH")
    email_base_path: str = Field(..., env="EMAIL_BASE_PATH")
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")

    class Config:
        env_file = None  # Do not read from .env file directly in Kubernetes

config = Config()
