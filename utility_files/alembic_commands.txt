alembic -h
alembic init alembic : To initialize alembic in project
alembic revision -m "Creating User table" : to create new version file


Exporting DB details: (Linux)

export DB_USER="postgres"
export DB_PASS="root123"
export DB_HOST="localhost"
export DB_NAME="EmailGenerator"


Exporting DB details: (Windows)

set DB_USER=postgres
set DB_PASS=root123
set DB_HOST=localhost
set DB_NAME=EmailGenerator

To confirmation : 
echo %VARIABLE_NAME%

alembic upgrade head --sql : To see sql query to be generated by sqlalchemy
alembic upgrade head : To upgrade current head version in db
alembic downgrade -1 : To downgrade db schema
alembic history : to check stack sequence
alembic current / head : to get head/top

alembic revision --autogenerate -m "Add new table"

to create model.py file

sqlacodegen postgresql+psycopg2://postgres:root123@localhost:5432/EmailGenerator --outfile models.py



