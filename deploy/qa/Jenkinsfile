podTemplate(
    containers: [
        containerTemplate(name: 'helm', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/lachlanevenson/k8s-helm:v3.4.2', command: 'cat', ttyEnabled: true),
        containerTemplate(name: 'curl', alwaysPullImage: true, image: 'nexus.sling-dev.com:8023/sling/jenkins/curl', command: 'cat', ttyEnabled: true)
    ],
    imagePullSecrets: ['registry-credentials']) {
  properties([parameters(
      [string(name: 'dockerImageTag', description: 'Docker image tag to deploy'),
       string(name: 'branchName', defaultValue: 'dev', description: 'Branch being deployed'),
       string(name: 'targetBranch', defaultValue: 'dev', description: 'Target branch against which if a PR is being raised')])])

  currentBuild.description = "branch ${params.branchName}"
  node(POD_LABEL) {
    container('helm') {
      withCredentials([[$class       : 'FileBinding',
                        credentialsId: 'sling-test-kubeconfig',
                        variable     : 'KUBECONFIG'],
                       [$class       : 'StringBinding',
                        credentialsId: 'sd-charts-github-api-token',
                        variable     : 'API_TOKEN']]) {
        stage('Add Helm repository') {
          sh script: "helm repo add stable 'https://charts.helm.sh/stable'",
              label: 'Add stable helm repo'
          sh script: "helm repo add sd-charts 'https://${API_TOKEN}@raw.githubusercontent.com/amuratech/sd-charts/dev/'",
              label: 'Add helm repo'
          sh script: 'helm repo list', label: 'List available helm repos'
        }
        withCredentials([[$class       : 'StringBinding',
                          credentialsId: 'test-env-postgres-password',
                          variable     : 'POSTGRES_PASSWORD'],
                          [$class       : 'StringBinding',
                          credentialsId: 'kylas-test-openai-api-key',
                          variable     : 'OPENAI_API_KEY'],
                          [$class       : 'StringBinding',
                          credentialsId: 'kylas-test-gemini-api-key',
                          variable     : 'GEMINI_API_KEY'],
                          [$class       : 'StringBinding',
                          credentialsId: 'test-env-rabbitmq-password',
                          variable     : 'RABBITMQ_PASSWORD'],
                          [$class       : 'StringBinding',
                          credentialsId: 'qa-stage-redis-host-url',
                          variable     : 'REDIS_HOST']]) {
          stage('Deploy') {
            echo "Deploying docker release -> nexus.sling-dev.com/8023/sling/sd-ai:${params.dockerImageTag}"

            sh script: "helm upgrade --install sd-ai sd-charts/sd-ai " +
                "--set appConfig.rabbitmq.password=${RABBITMQ_PASSWORD}," +
                "appConfig.postgres.password=${POSTGRES_PASSWORD}," +
                "image.tag=${params.dockerImageTag}," +
                "appConfig.activeProfile=QA," +
                "appConfig.openai.apikey=${OPENAI_API_KEY}," +
                "appConfig.gemini.apikey=${GEMINI_API_KEY}," +
                "appConfig.postgres.flags=?sslmode=disable," +
                "appConfig.redis.host=${REDIS_HOST}," +
                "deployment.annotations.buildNumber=${currentBuild.number} " +
                "--wait",
                label: 'Install helm release'
          }
        }
      }
    }

    container('curl') {
      stage('Refresh Gateway routes') {
        sh script: 'curl -X POST \\\n' +
            '  http://api-qa.sling-dev.com/actuator/gateway/refresh \\\n' +
            '  -H \'Accept: application/json\' \\\n' +
            '  -H \'Host: api-qa.sling-dev.com\' \\\n' +
            '  -H \'cache-control: no-cache\'', label: 'Force refresh routes cache'
      }
    }
  }
}
