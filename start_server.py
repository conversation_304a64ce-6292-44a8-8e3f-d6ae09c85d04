import sys
import uvicorn
import asyncio
from fastapi import FastAPI
from contextlib import asynccontextmanager
from app.api.v1.email_api_endpoint import router as email_router
from app.api.smart_list.v1.smart_list_api import router as smart_list_router
from app.api.whatsapp_templates.v1.whatsapp_templates_api_endpoint import \
  templates_router
from app.call_analysis.api.v1.call_analysis_api_endpoints import \
  router as call_analysis_router
from app.rabbitmq.config import setup_rabbitmq
from app.call_analysis.rabbitmq.config import \
  setup_rabbitmq as setup_call_analysis_rabbitmq
from app.note_analysis.rabbitmq.config import \
  setup_rabbitmq as setup_note_analysis_rabbitmq
from app.rabbitmq.listener import consume_contact_field_updated, \
  consume_deal_or_company_picklist_value_updated, consume_field_updated, \
  consume_picklist_value_updated, consume_messages, consume_new_field_created, \
  consume_user_layout_created
from app.call_analysis.rabbitmq.listener import consume_calllog
from app.note_analysis.rabbitmq.listener import consume_note
from loguru import logger
# Import Prometheus metrics
from app.metrics.prometheus_metrics import consumer_restart_counter, consumer_error_counter
from app.metrics.middleware import PrometheusMiddleware
from app.metrics.routes import router as metrics_router
from app.api.health.health_check import router as health_router  # Import the health check router
import time

async def auto_restart_consumer(consumer_func, *args, **kwargs):
  while True:
    try:
      logger.info(
        f"Starting consumer: {consumer_func.__name__} with args: {args}")
      # Record consumer restart
      consumer_restart_counter.labels(consumer=consumer_func.__name__).inc()

      # Wrap the consumer function to track metrics
      async def wrapped_consumer(*consumer_args, **consumer_kwargs):
        from app.metrics.prometheus_metrics import consumer_message_counter, consumer_processing_time
        queue_name = consumer_args[0].name if consumer_args and hasattr(consumer_args[0], 'name') else "unknown"

        # Track message processing
        start_time = time.time()
        try:
          result = await consumer_func(*consumer_args, **consumer_kwargs)
          consumer_message_counter.labels(
            consumer=consumer_func.__name__,
            queue=queue_name
          ).inc()
          consumer_processing_time.labels(
            consumer=consumer_func.__name__,
            queue=queue_name
          ).observe(time.time() - start_time)
          return result
        except Exception as e:
          # Track consumer errors
          consumer_error_counter.labels(
            consumer=consumer_func.__name__,
            queue=queue_name,
            error_type=type(e).__name__
          ).inc()
          raise

      # Call the wrapped consumer
      await wrapped_consumer(*args, **kwargs)

    except Exception as e:
      logger.error(f"Consumer {consumer_func.__name__} crashed: {e}",
                   exc_info=True)
      logger.info("Restarting consumer in 5 seconds...")
      await asyncio.sleep(5)


@asynccontextmanager
async def lifespan(app: FastAPI):
  connection, channel, queues = await setup_rabbitmq()
  app.state.rabbitmq_connection = connection
  app.state.rabbitmq_channel = channel
  app.state.rabbitmq_queues = queues

  tasks = [
    # User layout created
    # asyncio.create_task(auto_restart_consumer(consume_user_layout_created,
    #                                           queues["user_layout_created"],
    #                                           entity_type="lead")),
    # asyncio.create_task(auto_restart_consumer(consume_user_layout_created,
    #                                           queues[
    #                                             "new_user_deal_layout_created"],
    #                                           entity_type="deal")),
    # asyncio.create_task(auto_restart_consumer(consume_user_layout_created,
    #                                           queues[
    #                                             "new_user_contact_layout_created"],
    #                                           entity_type="contact")),
    # asyncio.create_task(auto_restart_consumer(consume_user_layout_created,
    #                                           queues[
    #                                             "new_user_company_layout_created"],
    #                                           entity_type="company")),

    # Field updated
    asyncio.create_task(
        auto_restart_consumer(consume_field_updated,
                              queues["lead_field_updated"],
                              entity_type="lead")),
    asyncio.create_task(
        auto_restart_consumer(consume_field_updated,
                              queues["deal_field_updated"],
                              entity_type="deal")),
    asyncio.create_task(auto_restart_consumer(consume_field_updated,
                                              queues["contact_field_updated"],
                                              entity_type="contact")),
    asyncio.create_task(auto_restart_consumer(consume_field_updated,
                                              queues["company_field_updated"],
                                              entity_type="company")),

    # Picklist value updated
    asyncio.create_task(auto_restart_consumer(consume_picklist_value_updated,
                                              queues[
                                                "lead_picklist_value_updated"],
                                              entity_type="lead")),
    asyncio.create_task(
        auto_restart_consumer(consume_deal_or_company_picklist_value_updated,
                              queues["deal_picklist_value_updated"],
                              entity_type="deal")),
    asyncio.create_task(auto_restart_consumer(consume_picklist_value_updated,
                                              queues[
                                                "contact_picklist_value_updated"],
                                              entity_type="contact")),
    asyncio.create_task(
        auto_restart_consumer(consume_deal_or_company_picklist_value_updated,
                              queues["company_picklist_value_updated"],
                              entity_type="company")),

    # New field created
    asyncio.create_task(auto_restart_consumer(consume_new_field_created,
                                              queues["lead_new_field_created"],
                                              entity_type="lead")),
    asyncio.create_task(auto_restart_consumer(consume_new_field_created,
                                              queues["deal_new_field_created"],
                                              entity_type="deal")),
    asyncio.create_task(auto_restart_consumer(consume_new_field_created, queues[
      "contact_new_field_created"], entity_type="contact")),
    asyncio.create_task(auto_restart_consumer(consume_new_field_created, queues[
      "company_new_field_created"], entity_type="company")),
  ]

  app.state.rabbitmq_tasks = tasks

  # Setup Call Analysis RabbitMQ
  call_analysis_connection, call_analysis_channel, call_analysis_queues = await setup_call_analysis_rabbitmq()
  app.state.call_analysis_rabbitmq_connection = call_analysis_connection
  app.state.call_analysis_rabbitmq_channel = call_analysis_channel
  app.state.call_analysis_rabbitmq_queues = call_analysis_queues

  call_analysis_tasks = [
    asyncio.create_task(auto_restart_consumer(consume_calllog,
                                              call_analysis_queues[
                                                "workflow_calllog"])),
  ]

  app.state.call_analysis_rabbitmq_tasks = call_analysis_tasks

  # Setup Note Analysis RabbitMQ
  note_analysis_connection, note_analysis_channel, note_analysis_queues = await setup_note_analysis_rabbitmq()
  app.state.note_analysis_rabbitmq_connection = note_analysis_connection
  app.state.note_analysis_rabbitmq_channel = note_analysis_channel
  app.state.note_analysis_rabbitmq_queues = note_analysis_queues

  note_analysis_tasks = [
    asyncio.create_task(auto_restart_consumer(consume_note,
                                              note_analysis_queues[
                                                "note_created"])),
  ]
  app.state.note_analysis_rabbitmq_tasks = note_analysis_tasks

  try:
    yield
  finally:
    for task in app.state.rabbitmq_tasks:
      task.cancel()
      try:
        await task
      except asyncio.CancelledError:
        pass
    await connection.close()

    for task in app.state.call_analysis_rabbitmq_tasks:
      task.cancel()
      try:
        await task
      except asyncio.CancelledError:
        pass

    for task in app.state.note_analysis_rabbitmq_tasks:
      task.cancel()
      try:
        await task
      except asyncio.CancelledError:
        pass

    print("RabbitMQ connection closed.")
    print("RabbitMQ tasks cancelled.")


def create_app():
  """Create and configure the FastAPI app."""
  app = FastAPI(
      lifespan=lifespan,
      openapi_url="/v2/api-docs",
  )

  # Add Prometheus middleware
  app.add_middleware(PrometheusMiddleware)

  # Include health check endpoints
  app.include_router(health_router, tags=["Health"])

  app.include_router(email_router, prefix="/v1/smart-assistant/emails",
                     tags=["Email"])
  app.include_router(smart_list_router, prefix="/v1/smart-assistant",
                     tags=["Smart List"])
  app.include_router(call_analysis_router, prefix="/v1/smart-assistant",
                     tags=["Call Analysis"])
  app.include_router(templates_router,
                     prefix="/v1/smart-assistant/whatsapp-templates",
                     tags=["WhatsApp Templates"])
  # Add metrics endpoint
  app.include_router(metrics_router, tags=["Metrics"])

  return app


def main():
  """Main function to handle script execution and start the server."""
  try:
    uvicorn.run(
        "start_server:create_app",
        host="0.0.0.0",
        port=8000,
        workers=4,
        factory=True,
        access_log=True,
        log_level="info"
    )

  except KeyboardInterrupt:
    print("\nServer shutdown requested. Exiting...")
  except Exception as e:
    print(f"Unexpected error: {e}")
    sys.exit(1)


if __name__ == "__main__":
  main()