from prometheus_client import Counter, Histogram, Gauge, Summary

# HTTP request metrics
http_request_counter = Counter(
    'http_requests_total', 
    'Total number of HTTP requests',
    ['method', 'endpoint', 'status_code']
)

http_request_duration = Histogram(
    'http_request_duration_seconds', 
    'HTTP request duration in seconds',
    ['method', 'endpoint']
)

# RabbitMQ consumer metrics
consumer_message_counter = Counter(
    'rabbitmq_messages_consumed_total', 
    'Total number of RabbitMQ messages consumed',
    ['consumer', 'queue']
)

consumer_error_counter = Counter(
    'rabbitmq_consumer_errors_total', 
    'Total number of errors in RabbitMQ consumers',
    ['consumer', 'queue', 'error_type']
)

consumer_restart_counter = Counter(
    'rabbitmq_consumer_restarts_total', 
    'Total number of RabbitMQ consumer restarts',
    ['consumer']
)

consumer_processing_time = Histogram(
    'rabbitmq_message_processing_seconds', 
    'Time taken to process a RabbitMQ message',
    ['consumer', 'queue']
)

# Token usage metrics
token_usage_counter = Counter(
    'token_usage_total', 
    'Total token usage',
    ['tenant_id', 'user_id', 'type']
)

# System metrics
active_requests = Gauge(
    'active_requests', 
    'Number of active requests'
)