import time
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from .prometheus_metrics import http_request_counter, http_request_duration, active_requests

class PrometheusMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        active_requests.inc()
        start_time = time.time()
        
        # Process the request and get the response
        try:
            response = await call_next(request)
        except Exception as e:
            # Handle exceptions and record metrics
            active_requests.dec()
            http_request_counter.labels(
                method=request.method, 
                endpoint=request.url.path, 
                status_code=500
            ).inc()
            raise e
        
        # Record metrics
        process_time = time.time() - start_time
        status_code = response.status_code
        
        http_request_counter.labels(
            method=request.method, 
            endpoint=request.url.path, 
            status_code=status_code
        ).inc()
        
        http_request_duration.labels(
            method=request.method, 
            endpoint=request.url.path
        ).observe(process_time)
        
        active_requests.dec()
        
        return response