from datetime import datetime
from enum import Enum
from typing import List

from pydantic import BaseModel, Field


# Add addon keys as required
class AddonType(Enum):
    whatsapp_business = "WhatsApp-Business"


class Plan(BaseModel):
    id: str
    name: str


class UsageInfo(BaseModel):
    used: float
    total: float


class EntityCount(BaseModel):
    entity: str
    count: float


class CustomFieldCount(BaseModel):
    entity: str
    count: float


class CustomLayoutCount(BaseModel):
    entity: str
    count: float


class Addon(BaseModel):
    display_name: str = Field(alias="displayName")
    total_count: float = Field(alias="totalCount")
    total_cost: float = Field(alias="totalCost")
    id: str


class StorageCount(BaseModel):
    entity: str
    count: float


class GoalCount(BaseModel):
    entity: str
    count: float


class TrialAddon(BaseModel):
    display_name: str = Field(alias="displayName")
    total_count: float = Field(alias="totalCount")
    total_cost: float = Field(alias="totalCost")
    start_at: datetime = Field(alias="startAt")
    end_at: datetime = Field(alias="endAt")
    active: bool


class UsageResponse(BaseModel):
    plan_name: str = Field(alias="planName")
    plan: Plan
    renewal_at: datetime = Field(alias="renewalAt")
    updated_at: datetime = Field(alias="updatedAt")

    # Usage information
    users: UsageInfo
    records: UsageInfo
    storage: UsageInfo
    workflows: UsageInfo
    email_templates: UsageInfo = Field(alias="emailTemplates")
    custom_fields: UsageInfo = Field(alias="customFields")
    market_place_apps: UsageInfo = Field(alias="marketPlaceApps")
    dashboards: UsageInfo
    layouts: UsageInfo
    goals: UsageInfo
    email_tracking: UsageInfo = Field(alias="emailTracking")
    forex: UsageInfo
    ip_whitelisting: UsageInfo = Field(alias="ipWhitelisting")
    field_sales: UsageInfo = Field(alias="fieldSales")
    whatsapp_business: UsageInfo = Field(alias="whatsappBusiness")
    whatsapp_credits: UsageInfo = Field(alias="whatsappCredits")
    bi_and_analytics: UsageInfo = Field(alias="biAndAnalytics")
    api_throttling_limit: UsageInfo = Field(alias="apiThrottlingLimit")
    scoring_rules: UsageInfo = Field(alias="scoringRules")
    campaigns: UsageInfo

    # Count arrays
    entity_counts: List[EntityCount] = Field(alias="entityCounts")
    custom_field_counts: List[CustomFieldCount] = Field(alias="customFieldCounts")
    custom_layout_counts: List[CustomLayoutCount] = Field(alias="customLayoutCounts")
    storage_counts: List[StorageCount] = Field(alias="storageCounts")
    goal_counts: List[GoalCount] = Field(alias="goalCounts")

    # Addons
    addons: List[Addon]
    trial_addons: List[TrialAddon] = Field(alias="trialAddons")

