class AuthenticationError(Exception):
  """Raised when authentication fails (e.g., invalid token, access denied)."""

  def __init__(self, message="JWT Authentication failed", errorCode="039001"):
    self.errorCode = errorCode
    self.message = message
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"errorCode": self.errorCode, "message": self.message}

  def __str__(self):
    return f"AuthenticationError({self.errorCode}): {self.message}"




class TokenLimitExceededException(Exception):
  def __init__(self, errorCode="039002", message="Token Limit Exceeded"):
    self.errorCode = errorCode
    self.message = message
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"errorCode": self.errorCode, "message": self.message}




class RateLimitExceededException(Exception):
  def __init__(self, errorCode="039003", message="Rate Limit Exceeded"):
    self.errorCode = errorCode
    self.message = message
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"errorCode": self.errorCode, "message": self.message}

  def __str__(self):
    return f"TokenLimitExceededException({self.errorCode}): {self.message}"




class NamespaceNotFoundException(Exception):
  def __init__(self, errorCode="039004",
      message="Namespace not found in vector database"):
    self.errorCode = errorCode
    self.message = message
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"errorCode": self.errorCode, "message": self.message}

  def __str__(self):
    return f"NamespaceNotFoundException({self.errorCode}): {self.message}"




class FiltersNotFoundException(Exception):
  def __init__(self, errorCode="039005",
      message="Filters not found in vector database"):
    self.errorCode = errorCode
    self.message = message
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"errorCode": self.errorCode, "message": self.message}

  def __str__(self):
    return f"FiltersNotFoundException({self.errorCode}): {self.message}"




class FileSizeLimitExceededException(Exception):
  """Raised when the file size exceeds the allowed limit."""

  def __init__(self, error_code="039006",
      message="File size exceeds the allowed limit"):
    self.error_code = error_code
    self.message = message
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"error_code": self.error_code, "message": self.message}

  def __str__(self):
    return f"FileSizeLimitExceededException({self.error_code}): {self.message}"




class ExternalAIModelException(Exception):
  """Raised when an error occurs while interacting with an external AI model API."""

  def __init__(self, error_code="039007",
      message="Error occurred while interacting with the external AI model"):
    self.error_code = error_code
    self.message = message
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"error_code": self.error_code, "message": self.message}

  def __str__(self):
    return f"ExternalAIModelException({self.error_code}): {self.message}"




class RabbitMQPublishException(Exception):
  """Raised when an error occurs while publishing a message to RabbitMQ."""

  def __init__(self, error_code="039008",
      message="Error occurred while publishing to RabbitMQ"):
    self.error_code = error_code
    self.message = message
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"error_code": self.error_code, "message": self.message}

  def __str__(self):
    return f"RabbitMQPublishException({self.error_code}): {self.message}"




class TaskFormFieldExtractionError(Exception):
  """Raised when there is an error extracting task form fields."""

  def __init__(self, error_code="039009",
      message="Error extracting task form fields"):
    self.error_code = error_code
    self.message = message
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"error_code": self.error_code, "message": self.message}

  def __str__(self):
    return f"TaskFormFieldExtractionError({self.error_code}): {self.message}"




class TaskCreationError(Exception):
  """Raised when there is an error creating a task."""

  def __init__(self, error_code="039010", message="Error creating task"):
    self.error_code = error_code
    self.message = message
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"error_code": self.error_code, "message": self.message}

  def __str__(self):
    return f"TaskCreationError({self.error_code}): {self.message}"



class CallAnalysisInProgressException(Exception):
    """Raised when call analysis is already in progress for the same call ID."""

    def __init__(self, error_code="039011",
        message="Call analysis is already in progress for this call ID"):
      self.error_code = error_code
      self.message = message
      super().__init__(self.message)

    def to_dict(self):
      """Returns the error message and code as a dictionary."""
      return {"error_code": self.error_code, "message": self.message}

    def __str__(self):
      return f"CallAnalysisInProgressException({self.error_code}): {self.message}"




class ResourceNotFoundException(Exception):
  """Raised when a resource is not found."""
  def __init__(self, error_code="039012", message="Resource not found"):
    self.errorCode = error_code
    self.message = message
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"errorCode": self.errorCode, "message": self.message}

  def __str__(self):
    return f"ResourceNotFoundException({self.errorCode}): {self.message}"




class AddonNotPresentException(Exception):
  """Raised when addon is not present"""
  def __init__(self, error_code="039013", addon_name="addon" ,message="Addon not present"):
    self.errorCode = error_code
    self.addon_name = addon_name
    self.message = f"You do not have {' '.join(addon_name.split('-'))} add-on present"
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"errorCode": self.errorCode, "message": self.message}

  def __str__(self):
    return f"AddonNotPresentException({self.errorCode}): {self.message}"




class InsufficientPermissionsException(Exception):
  """Raised when user does not have sufficient permissions"""
  def __init__(self, error_code="039014", permission="permission" , message="Insufficient permissions"):
    self.errorCode = error_code
    self.permission = permission
    self.message = f"You do not have sufficient permissions for this action"
    super().__init__(self.message)

  def to_dict(self):
    """Returns the error message and code as a dictionary."""
    return {"errorCode": self.errorCode, "message": self.message}

  def __str__(self):
    return f"InsufficientPermissionsException({self.errorCode}): {self.message}"
