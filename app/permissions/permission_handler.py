from enum import Enum
from typing import Optional

from loguru import logger

from app.permissions.permission_response_model import AuthResponse, Permission
from app.services.authentication_service.jwt_decoder import decode_token
from app.utils.error_handler import <PERSON>rror<PERSON><PERSON><PERSON>


# Add more permissions as required
class PermissionType(Enum):
    whatsappTemplate = "whatsappTemplate"

class PermissionAction(Enum):
    read = "read"
    write = "write"
    update = "update"
    delete = "delete"
    email = "email"
    call = "call"
    sms = "sms"
    task = "task"
    note = "note"
    meeting = "meeting"
    document = "document"
    read_all = "readAll"
    update_all = "updateAll"
    delete_all = "deleteAll"
    quotation = "quotation"
    reshare = "reshare"
    reassign = "reassign"



class PermissionHandler:
    def __init__(self):
        pass

    @staticmethod
    def _has_access(permission : Permission, action : PermissionAction) -> bool:
        if action.value == PermissionAction.read.value:
            return True if permission.action.read else False
        elif action.value == PermissionAction.write.value:
            return True if permission.action.write else False
        elif action.value == PermissionAction.update.value:
            return True if permission.action.update else False
        elif action.value == PermissionAction.delete.value:
            return True if permission.action.delete else False
        elif action.value == PermissionAction.email.value:
            return True if permission.action.email else False
        elif action.value == PermissionAction.call.value:
            return True if permission.action.call else False
        elif action.value == PermissionAction.sms.value:
            return True if permission.action.sms else False
        elif action.value == PermissionAction.task.value:
            return True if permission.action.task else False
        elif action.value == PermissionAction.note.value:
            return True if permission.action.note else False
        elif action.value == PermissionAction.meeting.value:
            return True if permission.action.meeting else False
        elif action.value == PermissionAction.document.value:
            return True if permission.action.document else False
        elif action.value == PermissionAction.read_all.value:
            return True if permission.action.read_all else False
        elif action.value == PermissionAction.update_all.value:
            return True if permission.action.update_all else False
        elif action.value == PermissionAction.delete_all.value:
            return True if permission.action.delete_all else False
        elif action.value == PermissionAction.quotation.value:
            return True if permission.action.quotation else False
        elif action.value == PermissionAction.reshare.value:
            return True if permission.action.reshare else False
        elif action.value == PermissionAction.reassign.value:
            return True if permission.action.reassign else False

        return False

    @staticmethod
    def has_permission(
            permission_type : PermissionType,
            permission_action : PermissionAction,
            token : str
    ) -> Optional[bool]:
        try:
            payload = decode_token(token)
            permissions = AuthResponse(**payload)
            req_permission : Optional[Permission] = None
            for permission in permissions.data.permissions:
                if permission.name == permission_type.value:
                    req_permission = permission

            if not req_permission:
                return False
            return PermissionHandler._has_access(permission=req_permission, action=permission_action)


        except Exception as e:
            logger.error(f"Unexpected error in has_permission function - Error: {str(e)}")
            ErrorHandler.handle_error(e)