from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime


class PermissionAction(BaseModel):
    """Model for permission actions within each permission"""
    read: bool = False
    write: bool = False
    update: bool = False
    delete: bool = False
    email: bool = False
    call: bool = False
    sms: bool = False
    task: bool = False
    note: bool = False
    meeting: bool = False
    document: bool = False
    read_all: bool = Field(default=False, alias="readAll")
    update_all: bool = Field(default=False, alias="updateAll")
    delete_all: bool = Field(default=False, alias="deleteAll")
    quotation: bool = False
    reshare: bool = False
    reassign: bool = False


class Permission(BaseModel):
    """Model for individual permissions"""
    id: int
    name: str
    description: str
    limits: int  # -1 indicates unlimited
    units: str
    action: PermissionAction


class AuthData(BaseModel):
    """Model for the authentication data"""
    access_token: str = Field(alias="accessToken")
    expires_in: int = Field(alias="expiresIn")
    expiry: str
    token_type: str = Field(alias="tokenType")
    refresh_token: str = Field(alias="refreshToken")
    permissions: List[Permission]

    class Config:
        allow_population_by_field_name = True


class AuthResponse(BaseModel):
    """Main model for the complete authentication response"""
    iss: str  # Issuer
    data: AuthData

    class Config:
        allow_population_by_field_name = True


# Alternative model with additional helper methods
class AuthResponseWithHelpers(AuthResponse):
    """Extended auth response model with helper methods"""

    def get_permission_by_name(self, name: str) -> Optional[Permission]:
        """Get a specific permission by name"""
        for permission in self.data.permissions:
            if permission.name == name:
                return permission
        return None

    def has_permission(self, name: str, action: str = "read") -> bool:
        """Check if a specific permission and action is allowed"""
        permission = self.get_permission_by_name(name)
        if not permission:
            return False
        return getattr(permission.action, action, False)

    def get_expiry_datetime(self) -> datetime:
        """Convert expiry timestamp to datetime object"""
        return datetime.fromtimestamp(self.data.expiry / 1000)

    def is_token_expired(self) -> bool:
        """Check if the access token is expired"""
        return datetime.now() > self.get_expiry_datetime()

    def get_permissions_with_limit(self, limit: int = -1) -> List[Permission]:
        """Get permissions with specific limit (-1 for unlimited)"""
        return [p for p in self.data.permissions if p.limits == limit]

    def get_unlimited_permissions(self) -> List[Permission]:
        """Get all permissions with unlimited access"""
        return self.get_permissions_with_limit(-1)

    def get_limited_permissions(self) -> List[Permission]:
        """Get all permissions with limited access"""
        return [p for p in self.data.permissions if p.limits > 0]


# Example usage models for FastAPI endpoints
class TokenRefreshRequest(BaseModel):
    """Request model for token refresh"""
    refresh_token: str = Field(alias="refreshToken")

    class Config:
        allow_population_by_field_name = True


class PermissionCheckRequest(BaseModel):
    """Request model for checking permissions"""
    resource: str
    action: str = "read"


class PermissionCheckResponse(BaseModel):
    """Response model for permission checks"""
    resource: str
    action: str
    allowed: bool
    permission_details: Optional[Permission] = Field(default=None, alias="permissionDetails")

    class Config:
        allow_population_by_field_name = True


class TokenInfo(BaseModel):
    """Model for token information"""
    access_token: str = Field(alias="accessToken")
    token_type: str = Field(alias="tokenType")
    expires_in: int = Field(alias="expiresIn")
    expiry_datetime: datetime = Field(alias="expiryDatetime")
    is_expired: bool = Field(alias="isExpired")

    class Config:
        allow_population_by_field_name = True


class PermissionSummary(BaseModel):
    """Summary model for user permissions"""
    total_permissions: int = Field(alias="totalPermissions")
    unlimited_permissions: int = Field(alias="unlimitedPermissions")
    limited_permissions: int = Field(alias="limitedPermissions")
    permission_names: List[str] = Field(alias="permissionNames")

    class Config:
        allow_population_by_field_name = True