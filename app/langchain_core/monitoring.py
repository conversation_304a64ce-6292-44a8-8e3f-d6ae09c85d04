"""
LangChain monitoring and observability utilities
"""
import os
from typing import Optional, Dict, Any
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.outputs import LLMResult
from langchain_core.messages import BaseMessage
from loguru import logger
from datetime import datetime
from config import config


class CustomCallback<PERSON>andler(BaseCallbackHandler):
    """Custom callback handler for monitoring LangChain operations"""
    
    def __init__(self, operation_name: str = "langchain_operation"):
        self.operation_name = operation_name
        self.start_time = None
        self.tokens_used = {"input": 0, "output": 0, "total": 0}
    
    def on_llm_start(
        self, 
        serialized: Dict[str, Any], 
        prompts: list[str], 
        **kwargs: Any
    ) -> None:
        """Called when LLM starts running"""
        self.start_time = datetime.now()
        logger.info(f"LLM started for operation: {self.operation_name}")
    
    def on_llm_end(self, response: LLMResult, **kwargs: Any) -> None:
        """Called when LLM ends running"""
        if self.start_time:
            duration = (datetime.now() - self.start_time).total_seconds()
            logger.info(f"LLM completed in {duration:.2f}s for operation: {self.operation_name}")
        
        # Extract token usage if available
        if response.llm_output and "token_usage" in response.llm_output:
            token_usage = response.llm_output["token_usage"]
            self.tokens_used = {
                "input": token_usage.get("prompt_tokens", 0),
                "output": token_usage.get("completion_tokens", 0),
                "total": token_usage.get("total_tokens", 0)
            }
            logger.info(f"Token usage: {self.tokens_used}")
    
    def on_llm_error(self, error: Exception, **kwargs: Any) -> None:
        """Called when LLM encounters an error"""
        logger.error(f"LLM error in operation {self.operation_name}: {str(error)}")
    
    def on_chain_start(
        self, 
        serialized: Dict[str, Any], 
        inputs: Dict[str, Any], 
        **kwargs: Any
    ) -> None:
        """Called when chain starts running"""
        logger.info(f"Chain started: {self.operation_name}")
    
    def on_chain_end(self, outputs: Dict[str, Any], **kwargs: Any) -> None:
        """Called when chain ends running"""
        logger.info(f"Chain completed: {self.operation_name}")
    
    def on_chain_error(self, error: Exception, **kwargs: Any) -> None:
        """Called when chain encounters an error"""
        logger.error(f"Chain error in {self.operation_name}: {str(error)}")


def     setup_langchain_monitoring():
    """Setup LangChain monitoring and tracing"""
    if config.langchain_tracing_v2:
        os.environ["LANGCHAIN_TRACING_V2"] = "true"
        
        if config.langchain_endpoint:
            os.environ["LANGCHAIN_ENDPOINT"] = config.langchain_endpoint
        
        if config.langchain_api_key:
            os.environ["LANGCHAIN_API_KEY"] = config.langchain_api_key
        
        if config.langchain_project:
            os.environ["LANGCHAIN_PROJECT"] = config.langchain_project
        
        logger.info("LangChain tracing enabled")
    else:
        logger.info("LangChain tracing disabled")


def get_callback_handler(operation_name: str) -> CustomCallbackHandler:
    """Get a callback handler for monitoring"""
    return CustomCallbackHandler(operation_name=operation_name)
