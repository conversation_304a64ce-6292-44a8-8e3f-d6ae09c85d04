import re
from typing import List, Dict, Any

class NotePreprocessor:
    """
    Note preprocessor using only regex and keyword checks.
    """
    def __init__(self):
        self._initialize_patterns()

    def _initialize_patterns(self):
        # Common noise patterns
        self.noise_patterns = [
            r'\b(?:test|testing|sample|example|dummy|placeholder)\b',
            r'\b(?:lorem ipsum|ipsum lorem)\b',
            r'^\s*[0-9]+\s*$',  # Just numbers
            r'^\s*[a-zA-Z]{1,2}\s*$',  # Just 1-2 letters
            r'^\s*[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]+\s*$',  # Just symbols
        ]
        self.min_chars = 5
        self.action_keywords = [
            'call', 'email', 'meet', 'schedule', 'follow up', 'review', 'check',
            'update', 'send', 'prepare', 'create', 'complete', 'finish', 'start',
            'discuss', 'analyze', 'research', 'investigate', 'contact', 'reach out',
            'remind', 'remember', 'todo', 'task', 'action', 'deadline', 'due date',
            'priority', 'urgent', 'important', 'need to', 'should', 'must', 'will',
            'initiate', 'compose', 'arrange', 'fix an appointment', 'circle back',
            're-examine', 'verify', 'revise', 'dispatch', 'draft', 'generate',
            'finalize', 'wrap up', 'deliberate', 'evaluate', 'explore', 'look into',
            'reach', 'connect', 'prompt', 'keep in mind', 'assignment', 'validate',
            'execution', 'cut-off', 'stipulated time', 'significance', 'utmost urgency',
            'crucial', 'respond', 'advised', 'mandatory', 'intend', 'notify',
            'arrange', 'organize', 'plan', 'set up', 'book', 'appoint',
            'reminder', 'reminded', 'reminding', 'reminds',
            'checking', 'checked', 'checks',
            'sending', 'sent', 'sends',
            'preparing', 'prepared', 'prepares',
            'creating', 'created', 'creates',
            'completing', 'completed', 'completes',
            'finishing', 'finished', 'finishes',
            'starting', 'started', 'starts',
            'discussing', 'discussed', 'discusses',
            'analyzing', 'analyzed', 'analyzes',
            'researching', 'researched', 'researches',
            'investigating', 'investigated', 'investigates',
            'contacting', 'contacted', 'contacts',
            'reaching out', 'reached out', 'reaches out',
            'reminding', 'reminded', 'reminds',
            'remembering', 'remembered', 'remembers',
            'todo', 'to-do', 'to do',
            'assign', 'assigned', 'assigning', 'assignment',
            'execute', 'executed', 'executing', 'execution',
            'prioritize', 'prioritized', 'prioritizing', 'priority',
            'urgent', 'urgency',
            'important', 'importance',
            'need', 'needed', 'needing',
            'should', 'must', 'will', 'would', 'shall',
            'approve', 'approved', 'approving',
            'submit', 'submitted', 'submitting',
            'reviewed', 'reviewing',
            'follow-up', 'followup',
            'remind', 'reminder',
            'escalate', 'escalated', 'escalating',
            'resolve', 'resolved', 'resolving',
            'address', 'addressed', 'addressing',
            'track', 'tracked', 'tracking',
            'monitor', 'monitored', 'monitoring',
            'report', 'reported', 'reporting',
            'summarize', 'summarized', 'summarizing',
            'confirm', 'confirmed', 'confirming',
            'inform', 'informed', 'informing',
            'notify', 'notified', 'notifying',
            'update', 'updated', 'updating',
            'share', 'shared', 'sharing',
            'assign', 'assigned', 'assigning',
            'delegate', 'delegated', 'delegating',
            'organize', 'organized', 'organizing',
            'plan', 'planned', 'planning',
            'arrange', 'arranged', 'arranging',
            'coordinate', 'coordinated', 'coordinating',
            'prepare', 'prepared', 'preparing',
            'schedule', 'scheduled', 'scheduling',
            'reschedule', 'rescheduled', 'rescheduling',
            'postpone', 'postponed', 'postponing',
            'cancel', 'cancelled', 'cancelling',
            'remind', 'reminded', 'reminding',
            'follow up', 'followed up', 'following up',
            'call', 'called', 'calling',
            'email', 'emailed', 'emailing',
            'meet', 'meeting', 'met',
            'send', 'sent', 'sending',
            'receive', 'received', 'receiving',
            'acknowledge', 'acknowledged', 'acknowledging',
            'accept', 'accepted', 'accepting',
            'decline', 'declined', 'declining',
            'reject', 'rejected', 'rejecting',
            'confirm', 'confirmed', 'confirming',
            'remind', 'reminded', 'reminding',
            'reminder',
        ]
        self.no_response_phrases = {
            "rnr", "didn't answer", "customer busy", "number busy", "no answer",
            "busy", "callback later", "ringing", "not responding", "not reachable",
            "switched off", "disconnected", "disconnect", "disconnecting",
            "no response", "didn't respond", "callback", "didn't pick",
            "not picked", "not connected", "not answered"
        }
        self.compiled_noise_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.noise_patterns]

    def clean_text(self, text: str) -> str:
        if not text or not isinstance(text, str):
            return ""
        cleaned = text.strip()
        cleaned = re.sub(r'\s+', ' ', cleaned)
        cleaned = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', cleaned)
        cleaned = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', cleaned)
        cleaned = re.sub(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', '', cleaned)
        cleaned = cleaned.strip()
        return cleaned

    def is_noise(self, text: str) -> bool:
        if not text:
            return True
        for pattern in self.compiled_noise_patterns:
            if pattern.search(text):
                return True
        if len(text.strip()) < self.min_chars:
            return True
        return False

    def has_no_response_phrase(self, text: str) -> List[str]:
        """Return a list of no-response phrases found in the text."""
        text_lower = text.lower()
        return [phrase for phrase in self.no_response_phrases if phrase in text_lower]

    def remove_no_response_phrases(self, text: str, found_phrases: List[str]) -> str:
        """Remove all found no-response phrases from the text."""
        result = text
        for phrase in found_phrases:
            # Remove phrase as a word or phrase, case-insensitive
            result = re.sub(re.escape(phrase), '', result, flags=re.IGNORECASE)
        # Remove extra whitespace
        result = re.sub(r'\s+', ' ', result).strip()
        return result

    def has_actionable_content(self, text: str) -> bool:
        if not text:
            return False
        text_lower = text.lower()
        for keyword in self.action_keywords:
            if keyword in text_lower:
                return True
        # Check for simple time/date words
        for time_word in ['today', 'tomorrow', 'week', 'month', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']:
            if time_word in text_lower:
                return True
        return False

    def should_process_with_ai(self, note_content: str) -> Dict[str, Any]:
        if not note_content:
            return {
                "should_process": False,
                "reason": "Empty or null content",
                "cleaned_content": "",
                "confidence": 0.0
            }
        cleaned_content = self.clean_text(note_content)
        if not cleaned_content:
            return {
                "should_process": False,
                "reason": "Content became empty after cleaning",
                "cleaned_content": "",
                "confidence": 0.0
            }
        # Check for no-response phrases first
        found_no_response = self.has_no_response_phrase(cleaned_content)
        if found_no_response:
            # Remove all found no-response phrases from the content
            content_without_no_response = self.remove_no_response_phrases(cleaned_content, found_no_response)
            # If the rest is empty or very short, do not process
            if len(content_without_no_response.strip()) < self.min_chars:
                return {
                    "should_process": False,
                    "reason": "Only no-response phrase or too little content besides no-response phrase.",
                    "cleaned_content": cleaned_content,
                    "confidence": 0.0,
                    "word_count": len(content_without_no_response.split()),
                    "has_actionable_content": False,
                    "has_no_response_phrase": True
                }
            # Otherwise, proceed as actionable
            else:
                has_actionable = self.has_actionable_content(content_without_no_response)
                confidence = 0.5
                if has_actionable:
                    confidence += 0.3
                word_count = len(content_without_no_response.split())
                if word_count > 10:
                    confidence += 0.1
                if word_count > 20:
                    confidence += 0.1
                should_process = confidence >= 0.6
                return {
                    "should_process": should_process,
                    "reason": "No response phrase detected with substantial actionable content" if has_actionable else "No response phrase but insufficient actionable content",
                    "cleaned_content": content_without_no_response,
                    "confidence": min(confidence, 1.0),
                    "word_count": word_count,
                    "has_actionable_content": has_actionable,
                    "has_no_response_phrase": True
                }
        if self.is_noise(cleaned_content):
            return {
                "should_process": False,
                "reason": "Content identified as noise/irrelevant",
                "cleaned_content": cleaned_content,
                "confidence": 0.8
            }
        has_actionable = self.has_actionable_content(cleaned_content)
        confidence = 0.5
        if has_actionable:
            confidence += 0.3
        word_count = len(cleaned_content.split())
        if word_count > 10:
            confidence += 0.1
        if word_count > 20:
            confidence += 0.1
        should_process = confidence >= 0.6
        return {
            "should_process": should_process,
            "reason": "Actionable content detected" if has_actionable else "Insufficient actionable content",
            "cleaned_content": cleaned_content,
            "confidence": min(confidence, 1.0),
            "word_count": word_count,
            "has_actionable_content": has_actionable,
            "has_no_response_phrase": False
        }

# Global instance for reuse
note_preprocessor = NotePreprocessor() 