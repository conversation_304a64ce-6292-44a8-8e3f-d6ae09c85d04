from http.client import HTTPException

import jwt
import datetime
import os
import requests
from loguru import logger

def build_jwt_token_for_analysis(user_id, tenant_id):
    """
    Build a JWT token with all permissions (task, customField, user) for internal use.
    """
    secret = os.getenv("SECRET_KEY", "test")  # Use a secure secret in production

    now = datetime.datetime.utcnow()
    expiry = now + datetime.timedelta(minutes=5)  # 5-minute expiry

    # Full permission list
    temp_permissions = [
        {
            "id": 1,
            "name": "user",
            "description": "has access to user resource",
            "action": {
                "read": True, "readAll": True, "write": True,
                "update": True, "updateAll": True
            }
        }
    ]

    # CoreAccessToken structure
    temp_core_access_token = {
        "accessToken": "internal-token",
        "expiresIn": 5 * 60 * 1000,  # 5 minutes in milliseconds
        "permissions": temp_permissions,
        "expiry": int(expiry.timestamp() * 1000),  # Milliseconds
        "tenantId": str(tenant_id),
        "userId": str(user_id),
        "tokenType": "Bearer",
        "meta": {
            "rate-limit": 5,
            "pid": 2
        }
    }

    payload = {
        "iss": "sell",
        "data": temp_core_access_token,
        "exp": expiry
    }

    temp_token = jwt.encode(payload, secret, algorithm="HS256")

    iam_user = get_tenant_creator_from_iam(tenant_id, temp_token)
    logger.info(f"Note Analysis : iam_user: {iam_user}")
    logger.info(f"Note Analysis : temp_core_access_token: {temp_core_access_token}")
    permissions = iam_user["permissions"]
    if not permissions:
        raise Exception("No permissions found for user")
    logger.info(f"Note Analysis : user_id: {user_id} , permissions : {permissions}")
    

    core_access_token = {
        "accessToken": "internal-token",
        "expiresIn": 5 * 60 * 1000,  # 5 minutes in milliseconds
        "permissions": permissions,
        "expiry": int(expiry.timestamp() * 1000),  # Milliseconds
        "tenantId": str(tenant_id),
        "userId": str(iam_user.get("id")),
        "tokenType": "Bearer",
        "meta": {
            "rate-limit": 5,
            "pid": 2
        }
    }

    payload = {
        "iss": "sell",
        "data": core_access_token,
        "exp": expiry
    }

    token = jwt.encode(payload, secret, algorithm="HS256")
    token=token if isinstance(token, str) else token.decode("utf-8")
    return token,str(iam_user.get("id"))



def get_tenant_creator_from_iam(tenant_id, token):

    base_url = os.getenv("IAM_BASE_PATH")

    if not base_url:
      logger.error(
          f"Note Analysis : IAM_BASE_PATH environment variable is not set - environment=")
      raise ValueError("IAM_BASE_PATH environment variable is missing")

    full_url = f"{base_url}/v1/tenants/{tenant_id}/creator"

    headers = {
      "Authorization": f"Bearer {token}"
    }

    try:
      response = requests.get(full_url, headers=headers)
      response.raise_for_status()

      user_data = response.json()
      logger.info(f"Note Analysis : User data fetched from IAM: {user_data}")

      extracted_data = {
        "id": user_data.get("id"),
        "name": f"{user_data.get('firstName', '')} {user_data.get('lastName', '')}".strip(),
        "tenant_id": user_data.get("tenantId"),
        "email": user_data.get("email"),
        "permissions": user_data.get("permissions"),
      }

      logger.info(f"Note Analysis : User data extracted from IAM: {extracted_data}")
      return extracted_data

    except requests.exceptions.RequestException as e:
      logger.error(
          f"Note Analysis : Request error in get_user_from_iam - Error details: {str(e)}"
      )
      raise HTTPException(status_code=400,
                          detail="Something didn't work as expected.")
