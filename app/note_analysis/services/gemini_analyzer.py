import google.generativeai as genai
import os
import re
import json
from loguru import logger
from app.exceptions import ExternalAIModelException

# Configure the API key
genai.configure(api_key="AIzaSyD9PnY1Ci0hJZSCL49zmKne2rLPjj6reDw")

def extract_action_items(text: str):
    action_items = []
    pattern = r'"actionItems":\s*(\[[^\]]*\])'
    match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
    if match:
        try:
            action_items = json.loads(match.group(1))
        except json.JSONDecodeError:
            logger.warning("Failed to parse actionItems JSON, falling back to regex extraction")
            flat_array = re.findall(r'"([^"]+)"', match.group(1))
            i = 0
            while i + 5 < len(flat_array):
                if (flat_array[i] == "actionItem" and
                    flat_array[i + 2] == "priority" and
                    flat_array[i + 4] == "dueDate"):
                    action_items.append({
                        "actionItem": flat_array[i + 1],
                        "priority": flat_array[i + 3],
                        "dueDate": flat_array[i + 5]
                    })
                    i += 6
                else:
                    logger.warning(f"Note Analysis : Unexpected action item structure at index {i}: {flat_array[i:i + 6]}")
                    i += 1
    else:
        logger.info("Note Analysis : No action items found in the response")
    return action_items

def analyze_with_gemini(prompt: str) -> tuple[list, dict]:
    """
    Analyzes the note content using Gemini API.
    Args:
        note_content (str): The content of the note to analyze.
        current_date_time_utc (str): Current UTC timestamp for default due dates
    Returns:
        tuple[list, dict]: Action items list and usage metadata
    """
    try:
        logger.info("Note Analysis : Inside analyze_with_gemini function")
        logger.info(f"Note Analysis : prompt : {prompt}")

        # Create a model instance
        model = genai.GenerativeModel(model_name="gemini-2.0-flash")

        logger.info("Generating prompt")
        prompt = prompt
        
       
        logger.info("Note Analysis : Prompt generated successfully")

        # Generate analysis
        response = model.generate_content(prompt)

        logger.info("Note Analysis : ------------------- Gemini Response ----------------")
        logger.info(response)

        # Extract action items using the robust extraction function
        action_items = extract_action_items(response.text)
        
        if not action_items:
            logger.warning("Note Analysis : No action items found in the response, returning empty list")
            action_items = []

        return action_items, response.usage_metadata

    except Exception as e:
        logger.error(f"Note Analysis : Unexpected error occurred while analyzing note with Gemini: {e}")
        raise ExternalAIModelException(message=str(e)) 