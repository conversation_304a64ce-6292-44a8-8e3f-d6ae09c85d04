import requests
import os
from loguru import logger

def is_note_analysis_feature_enabled(token):
    """
    Checks if the 'note' featureType is enabled in the response.
    :param token: The Bearer token for authentication
    :return: True if note feature is enabled, False otherwise
    :raises Exception: if the API call fails or returns an unexpected response
    """
    base_url = os.getenv("IAM_BASE_PATH")
    url = f"{base_url}/v1/tenants/smart-assistant"
    headers = {
        "Accept": "application/json",
        "Authorization": f"Bearer {token}"
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code != 200:
            raise Exception(f"Note AI API call failed with status code: {response.status_code}, body: {response.text}")
        data = response.json()
        logger.info(f"Note Analysis : Note AI API response: {data}")
        if not isinstance(data, list):
            raise Exception("Note AI API response is not a list as expected.")
        for feature in data:
            if (
                feature.get("featureType") == "note"
                and feature.get("featureResponse", {}).get("enabled") is True
            ):
                return True
        return False
    except requests.RequestException as e:
        raise Exception(f"Note AI API request failed: {e}")
    except Exception as e:
        raise Exception(f"Unexpected error Note AI: {e}")

    