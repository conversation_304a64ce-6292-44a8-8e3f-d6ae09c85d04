import os
from loguru import logger
from datetime import datetime, timezone

from app.db.repository.request_history_repository import RequestHistoryService
from app.db.repository.token_history_repository import TokenHistoryService
from app.note_analysis.models.note_analysis_models import NoteAnalysisResponse, ActionItem
from app.exceptions import ExternalAIModelException
from app.rate_limit.rate_limiter import MAX_GLOBAL_TPM as MAX_GLOBAL_TPM_OPENAI
# from app.rate_limit.gemini_rate_limiter import MAX_GLOBAL_TPM as MAX_GLOBAL_TPM_GEMINI
# from app.note_analysis.services.gemini_analyzer import analyze_with_gemini
from app.note_analysis.services.openai_analyzer import analyze_with_openai

def analyze_note(note_content: str, db_session, user_id, tenant_id, note_id, user_mentions_mapping) -> NoteAnalysisResponse:
    """
    Analyzes the note content.
    Args:
        note_content (str): The content of the note to analyze.
        db_session: Database session
        user_id: User ID
        tenant_id: Tenant ID
        note_id: Note ID
        user_mentions_mapping: Mapping of user placeholders to user names and ids
    Returns:
        NoteAnalysisResponse: The analysis response object.
    """
    request_history_service = RequestHistoryService(db_session)
    current_date_time_utc = datetime.now(timezone.utc).strftime(
        "%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

    try:
        logger.info("Note Analysis : Inside analyze_note function")
        logger.info(f"Note Analysis : note content inside anlyze_note function : {note_content}")

        prompt = f"""You are a helpful assistant that analyzes notes from kylas CRM to extract action items.
                  The note may contain user mentions in the form of placeholders like USER_user_id. For examle : USER_1234, USER_8123. 
                  Here is the mapping of user placeholders to user names and ids:
                  {user_mentions_mapping}
                  When you see a placeholder like USER_12345, replace it with the corresponding user name from the mapping above and add '@' before the user name.
                  
                  If the note content indicates that a mentioned user should be assigned the task (for example, phrases like '@abc please follow up', 'reach out to this lead @xyz', etc.), then set 'assignedTo' field in the action item with that mentioned user id from the mapping provided above. Otherwise, by default set assignedTo field to the current user id: {user_id}. If there are multiple users mentioned in the note content, then set assignedTo field to the most appropriate or first mentioned user id from the note provided.

                  Analyze the following note and extract any action items that need to be completed :
                  {note_content}

                  For each action item you find, provide:
                  1. A clear description of what needs to be done (with user placeholders replaced by user names)
                  2. A priority level (HIGH, MEDIUM, or LOW)
                  3. A due date in the format "YYYY-MM-DDTHH:mm:ss.sss+0000". If no specific due date is mentioned, set it to one day after {current_date_time_utc}
                  4. An 'assignedTo' field with the user id of the user to whom the task should be assigned.

                  Format your response in json format as follows:
                  {{
                      "actionItems": [
                          {{
                              "actionItem": "[clear description of what needs to be done]",
                              "priority": "[HIGH/MEDIUM/LOW]",
                              "dueDate": "[due date in proper datetime format]",
                              "assignedTo": "[assignedTo user's id]"
                          }},
                          ...
                      ]
                  }}

                  Strictly follow these Notes : 
                  1. Response must contain only json object of action items. If no action items found, then give it a blank reply. Do not include any other text in response.
                  2. response content should only contain the json object with action items and should not contain any other text.
                  3. At max 3 tasks/action items should be created.
                  """

        # Gemini
        # action_items, usage_metadata = analyze_with_gemini(prompt)
        # logger.info("Note Analysis : Successfully analyzed note with Gemini")
        # logger.info(f"Note Analysis : Received action items from gemini : {action_items}")

        # input_tokens = usage_metadata.prompt_token_count
        # output_tokens = usage_metadata.candidates_token_count
        # total_tokens = usage_metadata.total_token_count
        
        # OpenAI
        action_items, usage_metadata = analyze_with_openai(prompt)
        logger.info("Note Analysis : Successfully analyzed note with OpenAI")
        logger.info(f"Received action items from openai : {action_items}")

        input_tokens = usage_metadata["prompt_token_count"]
        output_tokens = usage_metadata["candidates_token_count"]
        total_tokens = usage_metadata["total_token_count"]
        
        logger.info(
            f"Note Analysis : Input Tokens: {input_tokens}, Output Tokens: {output_tokens}, Total Tokens: {total_tokens}")

        token_history_service = TokenHistoryService(db_session)
        token_history_service.update_or_insert_token_history(
            user_id=user_id,
            tenant_id=tenant_id,
            token_limit=MAX_GLOBAL_TPM_OPENAI,
            token_consumed=total_tokens,
            usage_date=datetime.now().date(),
        )
        logger.info("Note Analysis : Token history updated successfully")

        inserted_data = request_history_service.insert_request(
            user_id=user_id,
            tenant_id=tenant_id,
            requested_input={
                "noteId": note_id,
                "noteContent": note_content
            },
            generated_response={
                "actionItems": action_items,
            },
            request_date=datetime.now(timezone.utc),
            email_id=None,
            input_token_consumed=input_tokens,
            output_token_consumed=output_tokens,
            total_consumed=total_tokens,
            response_accepted_status=True,
            entity_id=note_id,
            entity_type="note",
            operation="note_analysis",
            status="COMPLETED"  
        )
        logger.info("Note Analysis : Request history updated successfully")

        # Convert action items to ActionItem objects
        action_item_objects = []
        for item in action_items:
            if isinstance(item, dict):
                action_item_objects.append(ActionItem(
                    actionItem=item.get("actionItem", ""),
                    priority=item.get("priority", "MEDIUM"),
                    dueDate=item.get("dueDate", current_date_time_utc),
                    assignedTo=item.get("assignedTo", user_id)
                ))
            else:
                # If it's just a string, create an ActionItem with default values
                action_item_objects.append(ActionItem(
                    actionItem=str(item),
                    priority="MEDIUM",
                    dueDate=current_date_time_utc,
                    assignedTo=[]
                ))

        response_obj = NoteAnalysisResponse(
            noteId=note_id,
            actionItems=action_item_objects,
        )

        return response_obj, action_items, inserted_data

    except Exception as e:
        logger.error(f"Note Analysis : Unexpected error occurred while analyzing note: {e}")
        raise ExternalAIModelException(message=str(e))
