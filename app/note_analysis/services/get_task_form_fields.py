import os
import requests
from loguru import logger
from app.exceptions import TaskFormFieldExtractionError
from app.note_analysis.models.note_analysis_models import TaskFormFieldsResponse, PriorityValue, OpenStatus

def get_task_form_fields(token: str) -> TaskFormFieldsResponse:
    """
    Gets task form fields from the Kylas API.
    Args:
        token (str): JWT token for authentication
    Returns:
        TaskFormFieldsResponse: The task form fields response
    Raises:
        TaskFormFieldExtractionError: If priority values or status ID is not found
    """
    # API URL and headers
    logger.info("Fetching task form fields from API")
    url = f"http://sd-config/v1/entities/task/fields"
    headers = {
        "Authorization" : f"Bearer {token}",
        "Content-Type": "application/json",
    }

    try:
        # Make the API call
        response = requests.get(url, headers=headers)
        logger.info("Note Analysis : Responded with task form status code: {response.status_code} - {response.reason}  - {response.text}  - {response.headers}  - {response.json()}")
        response.raise_for_status()  # Raise an exception for HTTP errors
        data = response.json()
        logger.info("Note Analysis : Data fetched successfully: {data}")

        # Initialize results
        priority_values = []
        open_status_id = None

        # Parse the response
        for field in data:
            # Extract priority picklist values
            if field.get("name") == "priority" and field.get("picklist"):
                for value in field["picklist"]["values"]:
                    priority_values.append(PriorityValue(
                        name=value["name"],
                        id=value["id"]
                    ))

            # Extract task status "OPEN"
            if field.get("name") == "status" and field.get("picklist"):
                for value in field["picklist"]["values"]:
                    if value["name"] == "OPEN":
                        open_status_id = OpenStatus(
                            name=value["name"],
                            id=value["id"]
                        )

        # Raise exceptions if required fields are missing
        if not priority_values or not open_status_id :
            error_msg = "Some fields are missing in the task_form_fields API response"
            logger.error(error_msg)
            raise TaskFormFieldExtractionError(message=error_msg)

        logger.info(f"Note Analysis : Extracted priority values: {priority_values}")
        logger.info(f"Note Analysis : Extracted open status ID: {open_status_id}")

        # Return the extracted values
        return TaskFormFieldsResponse(
            priority_values=priority_values,
            open_status_id=open_status_id
        )

    except requests.exceptions.RequestException as e:
        logger.error(f"Note Analysis : API call for task form fields failed : {e}")
        raise TaskFormFieldExtractionError(message=f"API call for task form fields failed : {e}")