import os
import json
import aio_pika
from loguru import logger
from app.note_analysis.models.note_analysis_models import NoteAnalysisResponse
from app.exceptions import RabbitMQPublishException

RABBITMQ_URL = os.getenv("RABBITMQ_URL")
AI_EXCHANGE = os.getenv("AI_EXCHANGE")

async def publish_note_analysis_response(response: NoteAnalysisResponse, metadata: dict, user_id: str):
    """
    Publishes the note analysis response to RabbitMQ.
    Args:
        response (NoteAnalysisResponse): The analysis response to publish
        metadata (dict): Additional metadata for the message
        user_id (str): The ID of the user who requested the analysis
    """
    try:
        # Connect to RabbitMQ
        connection = await aio_pika.connect_robust(RABBITMQ_URL)
        channel = await connection.channel()

        # Declare exchange
        exchange = await channel.declare_exchange(AI_EXCHANGE, aio_pika.ExchangeType.TOPIC, durable=True)
        
        # Prepare message
        message = {
            "entity": {
                "noteId": response.noteId,
                "actionItems": [item.model_dump() for item in response.actionItems]
            },
            "metadata": metadata,
            "userId": user_id
        }

        # Create message
        message = aio_pika.Message(
            body=json.dumps(message).encode(),
            content_type="application/json",
            delivery_mode=aio_pika.DeliveryMode.PERSISTENT
        )

        # Publish message
        await exchange.publish(
            message,
            routing_key=f"ai.note_analysis.generated"
        )

        logger.info(f"Note Analysis : Published note-analysis message to exchange '{AI_EXCHANGE}' with routing key 'ai.note_analysis.generated' with message: {message}")

        # Close connection
        await connection.close()

    except Exception as e:
        logger.error(f"Note Analysis : Error publishing note analysis response: {str(e)}")
        raise RabbitMQPublishException(
            message=f"Failed to publish note analysis response: {str(e)}",
            error_code="PUBLISH_ERROR"
        ) 