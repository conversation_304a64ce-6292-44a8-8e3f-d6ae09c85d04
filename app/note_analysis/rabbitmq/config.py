import aio_pika
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get RabbitMQ URL from .env
RABBITMQ_URL = os.getenv("RABBITMQ_URL")
if not RABBITMQ_URL:
    raise ValueError("RABBITMQ_URL is not set in the environment")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def setup_rabbitmq():
    try:
        connection = await aio_pika.connect_robust(RABBITMQ_URL)
        channel = await connection.channel()

        queues = {}
        ex_productivity = await channel.declare_exchange("ex.productivity", aio_pika.ExchangeType.TOPIC, durable=True)
        q_note_created = await channel.declare_queue("q.note.created.ai", durable=True)
        await q_note_created.bind(ex_productivity, routing_key="note.created")
        queues["note_created"] = q_note_created

        logger.info("Note Analysis : RabbitMQ setup completed!")
        return connection, channel, queues

    except Exception as e:
        logger.exception("Note Analysis : Error setting up RabbitMQ for note analysis")
        raise