import os
import json
from loguru import logger
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from sqlalchemy.pool import NullPool
from bs4 import BeautifulSoup

from app.note_analysis.services.analyze_note import analyze_note
from app.note_analysis.services.get_task_form_fields import get_task_form_fields
from app.call_analysis.services.create_task import create_task
from app.note_analysis.services.build_jwt_token_note import build_jwt_token_for_analysis
from app.note_analysis.services.note_preprocessor import note_preprocessor
from app.note_analysis.services.is_note_aanalysis_feature_enabled import is_note_analysis_feature_enabled
from app.db.repository.users_repository import UserService
from app.api.smart_list.v1.smart_list_api import get_user_from_iam

from app.exceptions import (
    TaskCreationError,
    ExternalAIModelException,
    RabbitMQPublishException,
    RateLimitExceededException,
    TaskFormFieldExtractionError
)

# from app.rate_limit.rate_limiter import rate_limiter as openai_rate_limiter, get_redis as openai_get_redis

DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_engine(DATABASE_URL, poolclass=NullPool)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()

def format_relations(relations: list):
    return [
        {
            "id": rel.get("entityId"),
            "entity": rel.get("entityType"),
            "name": "Entity Name"  # Placeholder
        }
        for rel in relations
    ]

async def consume_note(queue):
    db_session = next(get_db())
    async with queue.iterator() as queue_iter:
        async for message in queue_iter:
            logger.info(f"Note Analysis : Received note message from queue")
            async with message.process():
                try:
                    payload = json.loads(message.body.decode())
                except json.JSONDecodeError as err:
                    logger.error(f"Invalid JSON format: {err}")
                    continue

                logger.debug(f"Note payload: {payload}")

                entity = payload.get("entity", {})
                metadata = payload.get("metadata", {})

                note_id = entity.get("id")
                tenant_id = metadata.get("tenantId")
                user_id = metadata.get("userId")
                created_via_type = entity.get("createdViaType", "Web")
                created_via = metadata.get("createdVia", "UI")
                note_content = entity.get("description", "")
                note_mentions = entity.get("mentions", [])
                related_to = format_relations(entity.get("relations", []))

                soup = BeautifulSoup(note_content, 'html.parser')
                note_content = soup.get_text(separator=' ', strip=True)
                print("Note Analysis : Parsed note content:", note_content)

                if (created_via_type in ("Web", "Mobile")) and (created_via != "CONVERSION"):

                    logger.info(f"Note Analysis : Analyzing note {note_id} for tenant {tenant_id}")
                    logger.info(f"Note Analysis : Note created via type : {created_via_type} & created via : {created_via}")

                    try:
                        # redis = await openai_get_redis()
                        # await openai_rate_limiter(tenant_id, redis)

                        token, tenantUserId = build_jwt_token_for_analysis(user_id, tenant_id)

                        # check if user exists in db
                        user_service = UserService(db_session)
                        existing_user = user_service.get_user_by_id(user_id)
                        logger.info(f"Note Analysis : User data extracted from DB in create function - existing_user={existing_user}")

                        # if user does not exist in db, fetch user data from IAM
                        if not existing_user:
                            logger.info(f"Note Analysis : Fetching user data from IAM after DB lookup - existing_user={existing_user}")
                            iam_user = get_user_from_iam(user_id, token)
                            logger.info(f"Note Analysis : IAM user data retrieved successfully - iam_user={iam_user}")
                            if iam_user:
                                # Insert user into local service
                                existing_user = user_service.insert_user(
                                    id=iam_user["id"],
                                    name=iam_user["name"],
                                    email=iam_user["email"],
                                    tenant_id=iam_user["tenant_id"]
                                )
                                logger.info(f"Note Analysis : Inserted new user from IAM - user_id={existing_user.id}")

                            else:
                                logger.error(f"Note Analysis : User not found in IAM - user_id={user_id}")
                                continue
                        
                        if not is_note_analysis_feature_enabled(token):
                            logger.info("Note Analysis : Note AI feature is not enabled for this user/tenant")
                            continue

                        preprocessing_result = note_preprocessor.should_process_with_ai(note_content=note_content)
                        if not preprocessing_result["should_process"]:
                            logger.info(f"Note Analysis : Note skipped during preprocessing: {preprocessing_result['reason']}")
                            continue

                        cleaned_content = preprocessing_result["cleaned_content"]

                        # Build mapping for user mentions
                        user_mentions_mapping = {f"{{{{USER_{m['id']}}}}}": {"name": m["name"], "id": m["id"]} for m in note_mentions}
                        # Replace placeholders in note_content with user names
                        for placeholder, user_info in user_mentions_mapping.items():
                            note_content = note_content.replace(placeholder, user_info["name"])
                        # Pass mapping to analyze_note
                        with SessionLocal() as db:
                            response, action_items, _ = analyze_note(
                                note_content=cleaned_content,
                                db_session=db,
                                user_id=user_id,
                                tenant_id=tenant_id,
                                note_id=note_id,
                                user_mentions_mapping=user_mentions_mapping
                            )
                        if action_items:
                            logger.info(f"Action items extracted: {action_items}")
                            task_form_fields_response = get_task_form_fields(token)

                            for item in action_items:
                                try:
                                    assigned_to = item.get("assignedTo", user_id)
                                    await create_task(
                                        token=token,
                                        user_id=tenantUserId,
                                        tenant_id=tenant_id,
                                        assigned_to=assigned_to,
                                        task_form_fields_response=task_form_fields_response,
                                        action_item=item.get("actionItem"),
                                        priority=item.get("priority", "MEDIUM"),
                                        due_date=item.get("dueDate"),
                                        related_to=related_to
                                    )
                                    logger.info(f"Note Analysis : Task created for action item: {item.get('actionItem')} assigned to user {assigned_to}")
                                except TaskCreationError as e:
                                    logger.warning(f"Note Analysis : Task creation failed: {e}")

                        else:
                            logger.info("Note Analysis : No action items found in note analysis")
                    except ExternalAIModelException as e:
                        logger.error(f"Note Analysis : AI Model Error: {e.message}")
                    except RabbitMQPublishException as e:
                        logger.error(f"Note Analysis : RabbitMQ Publish Error: {e.message}")
                    except RateLimitExceededException as e:
                        logger.warning(f"Note Analysis : Rate limit exceeded: {e.message}")
                    except TaskFormFieldExtractionError as e:
                        logger.error(f"Note Analysis : Task form field extraction failed: {e}")
                    except Exception as e:
                        logger.exception(f"Note Analysis : Unexpected error during note processing: {e}")
                else:
                    logger.info(f"Note Analysis : Note created via type : {created_via_type} & created via : {created_via}, skipping AI analysis")

