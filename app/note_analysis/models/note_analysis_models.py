from pydantic import BaseModel
from typing import List, Optional, Union
from datetime import datetime


# Defined the request & response body model
class NoteAnalysisRequest(BaseModel):
    noteId: int

class ActionItem(BaseModel):
    actionItem: str
    priority: str
    dueDate: str

class NoteAnalysisResponse(BaseModel):
    noteId: int
    actionItems: List[ActionItem] = []

class PriorityValue(BaseModel):
    name: str
    id: int

class NoteAnalysisAcceptedResponse(BaseModel):
    message: str
    noteId: int

class OpenStatus(BaseModel):
    name: str
    id: int

class TaskFormFieldsResponse(BaseModel):
    priority_values: List[PriorityValue]
    open_status_id: Optional[OpenStatus] 