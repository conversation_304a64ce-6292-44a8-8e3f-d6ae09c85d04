from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text
import os
from app.db.repository.users_repository import UserService
from app.metrics.prometheus_metrics import http_request_counter, http_request_duration

router = APIRouter()

def get_db():
    from app.call_analysis.api.v1.call_analysis_api_endpoints import SessionLocal
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.get("/health", status_code=status.HTTP_200_OK)
async def health_check(db: Session = Depends(get_db)):
    """
    Health check endpoint for Kubernetes liveness and readiness probes.
    Checks:
    1. API server is running (by responding to this request)
    2. Database connection is working
    """
    try:
        # Check database connection
        db.execute(text("SELECT 1"))
        
        # Return success response
        return {
            "status": "healthy",
            "components": {
                "api": "up",
                "database": "up"
            }
        }
    except Exception as e:
        # If database check fails, return unhealthy status
        http_request_counter.labels(
            method="GET", 
            endpoint="/health", 
            status_code=503
        ).inc()
        
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service unhealthy: {str(e)}"
        )

@router.get("/health/liveness", status_code=status.HTTP_200_OK)
async def liveness_check():
    """
    Liveness probe endpoint for Kubernetes.
    Only checks if the API server is running.
    """
    return {"status": "alive"}

@router.get("/health/readiness", status_code=status.HTTP_200_OK)
async def readiness_check(db: Session = Depends(get_db)):
    """
    Readiness probe endpoint for Kubernetes.
    Checks if the service is ready to accept traffic by verifying:
    1. API server is running
    2. Database connection is working
    """
    try:
        # Check database connection
        db.execute(text("SELECT 1"))
        
        # Return success response
        return {
            "status": "ready",
            "components": {
                "api": "ready",
                "database": "ready"
            }
        }
    except Exception as e:
        http_request_counter.labels(
            method="GET", 
            endpoint="/health/readiness", 
            status_code=503
        ).inc()
        
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service not ready: {str(e)}"
        )