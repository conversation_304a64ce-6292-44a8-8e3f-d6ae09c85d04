import json
import os
import requests
import traceback


from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import JSONResponse
from openai import OpenAI, RateLimitError
from fastapi.security import OAuth2PasswordBearer

from sqlalchemy.orm import Session

from app.api.v1.email_api_endpoint import get_db
from app.db.repository.users_repository import UserService
from app.exceptions import AuthenticationError, TokenLimitExceededException, NamespaceNotFoundException, FiltersNotFoundException, RateLimitExceededException
from app.rate_limit.rate_limiter import get_redis, rate_limiter
from app.services.authentication_service.jwt_decoder import validate_user_request
from app.services.listing_ai_services.generate_jsonrule import generate_filters
from app.services.listing_ai_services.listing_pydantic_models import (
    ListingAiRequestFormat,
    ListingAiAPIResponseFormat,
)
from app.services.listing_ai_services.vector_db_operations.elastic_vector_db_operations import \
    preprocess_filters, bulk_index_filters, create_index_and_alias, \
    create_index_for_standard_picklist, \
    preprocess_filters_for_standard_picklists
from app.services.logger_config import logger

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

router = APIRouter()


@router.post("", response_model=ListingAiAPIResponseFormat)
async def generate_json_rule_search(
    request: ListingAiRequestFormat,
    token: str = Depends(oauth2_scheme),
    db_session: Session = Depends(get_db),
    # redis=Depends(get_redis)
) -> ListingAiAPIResponseFormat:
    try:
        tenant_id, user_id, username = validate_user_request(token)
        # await rate_limiter(tenant_id, redis)

        logger.info(
            f"Generating filter - tenantId={tenant_id}, userId={user_id}, username={username}, requestContent={request.userQuery}"
        )

        user_service = UserService(db_session)

        existing_user = user_service.get_user_by_id(user_id)
        logger.info(
            f"User data extracted from DB in create function - existing_user={existing_user}"
        )
        if not existing_user:
            logger.info(
                f"Fetching user data from IAM after DB lookup - existing_user={existing_user}"
            )
            iam_user = get_user_from_iam(user_id, token)
            logger.info(
                f"IAM user data retrieved successfully - iam_user={iam_user}"
            )
            if iam_user:
                # Insert user into local service
                existing_user = user_service.insert_user(
                    id=iam_user["id"],
                    name=iam_user["name"],
                    email=iam_user["email"],
                    tenant_id=iam_user["tenant_id"]
                )
                logger.info(
                    f"Inserted new user from IAM - user_id={existing_user.id}"
                )

            else:
                logger.error(
                    f"User not found in IAM - user_id={user_id}"
                )
                raise HTTPException(status_code=400,
                                    detail="User not found")

        generated_filters = generate_filters(
            user_id=user_id,
            user_query=request.userQuery,
            namespace=tenant_id,
            db_session=db_session,
            tenant_id=tenant_id,
            request=request,
            token=token,
        )

        logger.info(
            f"Generated filters - tenantId={tenant_id}, userId={user_id}, username={username}, generatedFilters={generated_filters}"
        )

        json_rule = generated_filters["jsonRule"]
        if not json_rule.get("rules"):
          json_rule = None

        return ListingAiAPIResponseFormat(
            jsonRule= json_rule,
            entityType=generated_filters["entityType"],
            operation="search",
        )


    except RateLimitExceededException as rate_exc:
        logger.error(
            "Rate Limit Exceeded while generating smart filter",
            extra={"error": str(rate_exc)},
        )
        raise HTTPException(status_code=429, detail=rate_exc.to_dict())

    except AuthenticationError as auth_exc:
        logger.error(
            "Authentication Error while generating search json rule",
            extra={"error": str(auth_exc)},
        )
        raise HTTPException(status_code=401, detail=auth_exc.to_dict())

    except TokenLimitExceededException as token_exc:
        logger.error(
            "Token Limit Exceeded while generating search json rule",
            extra={"error": str(token_exc)},
        )
        raise HTTPException(status_code=429, detail=token_exc.to_dict())

    except NamespaceNotFoundException as ns_exc:
        logger.error(
            "Namespace not found while generating search json rule",
            extra={"error": str(ns_exc)},
        )
        raise HTTPException(status_code=400, detail=ns_exc.to_dict())

    except FiltersNotFoundException as filter_exc:
        logger.error(
            "Filters not found while generating search json rule",
            extra={"error": str(filter_exc)},
        )
        raise HTTPException(status_code=400, detail=filter_exc.to_dict())

    except Exception as e:
        logger.error(
            "Unexpected error while generating search json rule",
            extra={"error": str(e)},
        )
        raise HTTPException(
            status_code=400,
            detail="Something didn't work as expected in generate_json_rule_search."
            + str(e),
        )


@router.post("/{entityType}/create-elastic-list-layout")
async def create_elastic_list_layout(entityType: str, request: Request):
    try:
        logger.info(
            f"Creating elastic list layout for entityType={entityType} with request body: {await request.body()}"
        )

        tenant_id = request.query_params.get("tenantId")

        entityType = entityType.lower()

        if tenant_id is None:
            return JSONResponse(
                status_code=400,
                content={
                    "status": "FAILURE",
                    "message": "Missing tenantId in query parameters",
                },
            )

        alias_name=create_index_and_alias(entityType+"-field-index-1", tenant_id,entityType)

        layout_data = await request.body()
        layout_data = json.loads(layout_data)
        entity_type = entityType
        layout_data = {"event": layout_data}

        preprocessed_filters = preprocess_filters(layout_data, tenant_id, entity_type)

        logger.info(
            f"Preprocessed filters ={preprocessed_filters}"
        )

        bulk_index_filters(preprocessed_filters, tenant_id, alias_name,entity_type)

        logger.info(
            f"Inserted data into vectordb for tenantId={tenant_id}, entityType={entity_type}"
        )

        return JSONResponse(
            content={"status": "SUCCESS", "message": "Layout created successfully"}
        )

    except Exception as e:
        tb_str = traceback.format_exc()

        logger.info("Exception occurred in create_list_layout:\n" + tb_str)

        return JSONResponse(
            status_code=500,
            content={
                "status": "FAILURE",
                "message": f"Failed to create layout: {str(e)}",
            },
        )

@router.post("/{entityType}/sync-standard-picklist-vector-es")
async def create_standard_picklist(entityType: str, request: Request):
    try:
        logger.info(
            f"Creating sync-standard-picklist-vector-es list layout for entityType={entityType} with request body: {await request.body()}"
        )

        tenant_id = request.query_params.get("tenantId")

        entityType = entityType.lower()

        if tenant_id is None:
            return JSONResponse(
                status_code=400,
                content={
                    "status": "FAILURE",
                    "message": "Missing tenantId in query parameters",
                },
            )

        if entityType != "company":
            create_index_for_standard_picklist("standard-picklist")
            alias_name="standard-picklist"
        else:
            create_index_for_standard_picklist("standard-picklist-company")
            alias_name = "standard-picklist-company"

        layout_data = await request.body()
        layout_data = json.loads(layout_data)
        entity_type = entityType

        logger.info(
            f"Received data for standard picklist ={layout_data}"
        )
        preprocessed_filters = preprocess_filters_for_standard_picklists(layout_data, tenant_id, entity_type)

        logger.info(
            f"Preprocessed filters standard ={preprocessed_filters}"
        )

        bulk_index_filters(preprocessed_filters, tenant_id, alias_name,"standard-picklist")

        logger.info(
            f"Inserted data into vectordb for tenantId={tenant_id}, entityType={entity_type}"
        )

        return JSONResponse(
            content={"status": "SUCCESS", "message": "Layout created successfully"}
        )

    except Exception as e:
        tb_str = traceback.format_exc()

        logger.info("Exception occurred in create_list_layout:\n" + tb_str)

        return JSONResponse(
            status_code=500,
            content={
                "status": "FAILURE",
                "message": f"Failed to create layout: {str(e)}",
            },
        )



def get_user_from_iam(user_id, token):

    base_url = os.getenv("IAM_BASE_PATH")

    if not base_url:
      logger.error(
          f"IAM_BASE_PATH environment variable is not set - environment=")
      raise ValueError("IAM_BASE_PATH environment variable is missing")

    full_url = f"{base_url}/v1/users/{user_id}"

    headers = {
      "Authorization": f"Bearer {token}"
    }

    try:
      response = requests.get(full_url, headers=headers)
      response.raise_for_status()

      user_data = response.json()
      logger.info(f"User data fetched from IAM: {user_data}")

      extracted_data = {
        "id": user_data.get("id"),
        "name": f"{user_data.get('firstName', '')} {user_data.get('lastName', '')}".strip(),
        "tenant_id": user_data.get("tenantId"),
        "email": user_data.get("email"),
        "permissions": user_data.get("permissions"),
      }

      logger.info(f"User data extracted from IAM: {extracted_data}")
      return extracted_data

    except requests.exceptions.RequestException as e:
      logger.error(
          f"Request error in get_user_from_iam - Error details: {str(e)}"
      )
      raise HTTPException(status_code=400,
                          detail="Something didn't work as expected.")
