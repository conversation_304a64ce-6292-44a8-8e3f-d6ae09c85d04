from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.security import OAuth2PasswordBearer
from dotenv import load_dotenv
import os

from app.rate_limit.rate_limiter import rate_limiter, get_redis
from app.services.authentication_service.jwt_decoder import validate_user_request
from app.services.email_generate_service.email_model_class import EmailWriter
from app.services.email_generate_service.email_models import (
    NewEmailRequestFormat,
    ReplyEmailRequestFormat,
    RewriteEmailRequestFormat,
    EmailResponseFormat,
)
from app.services.getting_previous_emails.get_previous_emails import (
    get_previous_emails_content,
)
from app.exceptions import AuthenticationError, TokenLimitExceededException, \
    RateLimitExceededException
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import NullPool
from app.services.logger_config import logger

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL")

engine = create_engine(DATABASE_URL, poolclass=NullPool)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()
        
        
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

router = APIRouter()

async def get_email_handler() -> EmailWriter:
    return EmailWriter()


@router.post("/compose", response_model=EmailResponseFormat)
async def generate_new_email(
    request: NewEmailRequestFormat,
    email_handler: EmailWriter = Depends(get_email_handler),
    token: str = Depends(oauth2_scheme),
    db_session: Session = Depends(get_db),
    # redis=Depends(get_redis)
) -> EmailResponseFormat:
    try:
        tenant_id, user_id, username = validate_user_request(token)
        # await rate_limiter(tenant_id, redis)

        logger.info(
            f"Generating new email - tenantId={tenant_id}, userId={user_id}, username={username}, requestContent={request.content}"
        )

        email_output, response_id = await email_handler.get_new_email_response(
            request.content,
            tenant_id=tenant_id,
            user_id=user_id,
            user_name=username,
            request=request,
            token=token,
            db_session=db_session
        )

        logger.info(
            f"Email generated successfully - responseId={response_id}, userId={user_id}"
        )

        return EmailResponseFormat(
            content={
                "id": response_id,
                "subject": (
                    request.subject if request.subject else email_output.subject
                ),
                "content": email_output.body,
            }
        )

    except AuthenticationError as auth_exc:
        logger.error("Authentication Error while generating new email", extra={"error": str(auth_exc)})
        raise HTTPException(status_code=401, detail=auth_exc.to_dict())

    except TokenLimitExceededException as token_exc:
        logger.error("Token Limit Exceeded while generating new email", extra={"error": str(token_exc)})
        raise HTTPException(status_code=429, detail=token_exc.to_dict())

    except RateLimitExceededException as token_exc:
        logger.error("Rate Limit Exceeded while generating new email",
                     extra={"error": str(token_exc)})
        raise HTTPException(status_code=429, detail=token_exc.to_dict())

    except Exception as e:
        logger.error("Unexpected error while generating new email" + str(e))
        raise HTTPException(status_code=400, detail="Something didn't work as expected.")


@router.post("/reply", response_model=EmailResponseFormat)
async def generate_reply_email(
    request: ReplyEmailRequestFormat,
    email_handler: EmailWriter = Depends(get_email_handler),
    token: str = Depends(oauth2_scheme),
    db_session: Session = Depends(get_db),
    redis=Depends(get_redis)
) -> EmailResponseFormat:
    try:
        tenant_id, user_id, username = validate_user_request(token)
        await rate_limiter(tenant_id, redis)

        logger.info(
            f"Generating reply email - tenantId={tenant_id}, userId={user_id}, emailId={request.emailId}"
        )

        previous_conversation_history = get_previous_emails_content(
            request.emailId, 10, token
        )

        logger.info(
            f"Inside email end point previous_conversation_history={previous_conversation_history}, userId={user_id}"
        )

        email_output, response_id = await email_handler.get_reply_email_response(
            prev_email_list=previous_conversation_history["content"],
            tenant_id=tenant_id,
            user_id=user_id,
            request=request,
            token=token,
            db_session=db_session,
            system_prompt=request.content
        )

        logger.info(
            f"Reply email generated successfully - responseId={response_id}, userId={user_id}"
        )

        return EmailResponseFormat(
            content={
                "id": response_id,
                "subject": (
                    request.subject if request.subject else email_output.subject
                ),
                "content": email_output.body,
            }
        )

    except AuthenticationError as auth_exc:
        logger.error("Authentication Error while replying email", extra={"error": str(auth_exc)})
        raise HTTPException(status_code=403, detail=auth_exc.to_dict())

    except TokenLimitExceededException as token_exc:
        logger.error("Token Limit Exceeded while replying email", extra={"error": str(token_exc)})
        raise HTTPException(status_code=404, detail=token_exc.to_dict())

    except RateLimitExceededException as token_exc:
        logger.error("Rate Limit Exceeded while generating new email",
                     extra={"error": str(token_exc)})
        raise HTTPException(status_code=429, detail=token_exc.to_dict())

    except Exception as e:
        logger.error(
            "Unexpected error while replying email",
            exc_info=e
        )
        raise HTTPException(
            status_code=400,
            detail="Internal server error",
        )


@router.post("/{request_id}/accepted", response_model=dict)
async def accept_email_response(
    request_id: int,
    email_handler: EmailWriter = Depends(get_email_handler),
    token: str = Depends(oauth2_scheme),
    db_session: Session = Depends(get_db)
) -> dict:
    try:
        access_granted, _, _ = validate_user_request(token)

        if not access_granted:
            raise AuthenticationError("Access denied")

        email_handler.update_response_status_accepted(request_id=request_id, status=True, db_session=db_session)

        logger.info(
            f"Email response accepted - requestId={request_id}"
        )

        return {"status": "success"}

    except AuthenticationError as auth_exc:
        logger.error("Authentication Error while accepting status", extra={"error": str(auth_exc)})
        raise HTTPException(status_code=403, detail=auth_exc.to_dict())

    except Exception as e:
        logger.error("Unexpected error while accepting mail", extra={"error": str(e)})
        raise HTTPException(status_code=400, detail="Internal server error")



@router.post("/{email_id}/rewrite", response_model=EmailResponseFormat)
async def rewrite_email(
    email_id: int,
    request: RewriteEmailRequestFormat,
    email_handler: EmailWriter = Depends(get_email_handler),
    token: str = Depends(oauth2_scheme),
    db_session: Session = Depends(get_db),
    redis=Depends(get_redis)
) -> EmailResponseFormat:
    try:
        tenant_id, user_id, username = validate_user_request(token)
        await rate_limiter(tenant_id, redis)

        logger.info(
            f"Rewriting email - tenantId={tenant_id}, userId={user_id}, username={username}, emailId={email_id}"
        )



        # Call OpenAI to rewrite the email
        rewritten_email, response_id = await email_handler.get_rewritten_email_response(
            email_id=email_id,
            tenant_id=tenant_id,
            user_id=user_id,
            instruction=request.instruction,
            token=token,
            db_session=db_session
        )

        logger.info(
            f"Email rewritten successfully - responseId={response_id}, userId={user_id}"
        )

        return EmailResponseFormat(
            content={
                "id": response_id,
                "subject": rewritten_email.subject,
                "content": rewritten_email.body,
            }
        )

    except AuthenticationError as auth_exc:
        logger.error("Authentication Error while rewriting email", extra={"error": str(auth_exc)})
        raise HTTPException(status_code=401, detail=auth_exc.to_dict())

    except RateLimitExceededException as token_exc:
        logger.error("Rate Limit Exceeded while generating new email",
                     extra={"error": str(token_exc)})
        raise HTTPException(status_code=429, detail=token_exc.to_dict())

    except Exception as e:
        logger.error("Unexpected error while rewriting email", extra={"error": str(e)})
        raise HTTPException(status_code=400, detail="Something didn't work as expected.")
