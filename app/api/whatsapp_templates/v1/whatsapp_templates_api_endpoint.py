import os
from typing import Optional

from dotenv import load_dotenv
from fastapi import APIRouter
from fastapi.params import Depends
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool

from app.exceptions import AuthenticationError, InsufficientPermissionsException, AddonNotPresentException
from app.permissions.permission_handler import PermissionHandler, PermissionType, PermissionAction
from app.rate_limit.rate_limiter import get_redis, rate_limiter
from app.services.authentication_service.jwt_decoder import validate_user_request
from app.services.template_generate_service.template_generator import TemplateGenerator
from app.services.template_generate_service.template_models import TemplateResponseFormat, TemplateRequestFormat
from app.usage.usage_model import AddonType
from app.utils.app_utils import AppUtils
from app.utils.error_handler import ErrorHandler

load_dotenv()
DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_engine(DATABASE_URL,poolclass=NullPool)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")
templates_router = APIRouter()

async def get_templates_handler() -> TemplateGenerator:
    session = next(get_db())
    return TemplateGenerator(db_session=session)

async def check_whatsapp_access(token : str) :
    has_template_create_permission = PermissionHandler.has_permission(
        permission_type=PermissionType.whatsappTemplate,
        permission_action=PermissionAction.write,
        token=token
    )

    has_template_update_permission = PermissionHandler.has_permission(
        permission_type=PermissionType.whatsappTemplate,
        permission_action=PermissionAction.update_all,
        token=token
    )

    if not (has_template_create_permission or has_template_update_permission):
        raise InsufficientPermissionsException(
            permission=f"{PermissionType.whatsappTemplate.value} - {PermissionAction.write.value}"
        )

    has_whatsapp_addon = await AppUtils.has_addon(addon=AddonType.whatsapp_business, token=token)

    if not has_whatsapp_addon:
        raise AddonNotPresentException(addon_name=AddonType.whatsapp_business.value)

@templates_router.post("/compose", response_model=TemplateResponseFormat)
async def generate_template(
        request : TemplateRequestFormat,
        template_generator: TemplateGenerator = Depends(get_templates_handler),
        token : str = Depends(oauth2_scheme),
        redis = Depends(get_redis)
) -> Optional[TemplateResponseFormat]:
    try:
        tenant_id,user_id,username = validate_user_request(token=token)
        await rate_limiter(tenant_id, redis)
        await check_whatsapp_access(token=token)

        template = await template_generator.create_template(
            tenant_id=tenant_id,
            user_id=user_id,
            token=token,
            template_request=request,
        )
        return template
    except Exception as e:
        ErrorHandler.handle_error(message=str(e),exception=e)

@templates_router.post("/{template_id}/rewrite", response_model=TemplateResponseFormat)
async def rewrite_template(
        template_id: int,
        request : TemplateRequestFormat,
        template_generator: TemplateGenerator = Depends(get_templates_handler),
        token : str = Depends(oauth2_scheme),
        redis = Depends(get_redis)
) -> Optional[TemplateResponseFormat]:
    try:
        tenant_id,user_id,username = validate_user_request(token=token)
        await rate_limiter(tenant_id, redis)
        await  check_whatsapp_access(token=token)

        template = await template_generator.rewrite_template(
            template_id=template_id,
            tenant_id=tenant_id,
            user_id=user_id,
            token=token,
            template_request=request,
        )
        return template
    except Exception as e:
        ErrorHandler.handle_error(message=str(e),exception=e)

@templates_router.post("/{template_id}/accepted", response_model=Optional[dict[str, str]])
async def accept_template(
        template_id: int,
        token : str = Depends(oauth2_scheme),
        template_generator: TemplateGenerator = Depends(get_templates_handler),
        redis = Depends(get_redis)
) -> Optional[dict[str, str]]:
    try:
        tenant_id, user_id, username = validate_user_request(token=token)
        await rate_limiter(tenant_id, redis)
        await check_whatsapp_access(token=token)

        if not tenant_id:
            raise AuthenticationError("Access denied")

        success = await template_generator.mark_template_as_accepted(
            template_id=template_id,
            user_id=user_id,
            token=token,
        )

        return {
            "status" : f"{'success' if success else 'failed'}"
        }
    except Exception as e:
        ErrorHandler.handle_error(message=str(e),exception=e)
