import aio_pika
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get RabbitMQ URL from .env
RABBITMQ_URL = os.getenv("RABBITMQ_URL")

if not RABBITMQ_URL:
    raise ValueError("RABBITMQ_URL is not set in the environment")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def setup_rabbitmq():
    try:
        connection = await aio_pika.connect_robust(RABBITMQ_URL)
        channel = await connection.channel()

        queues = {}

        # ex_iam = await channel.declare_exchange("ex.iam", aio_pika.ExchangeType.TOPIC, durable=True)
        ex_config = await channel.declare_exchange("ex.config", aio_pika.ExchangeType.TOPIC, durable=True)
        ex_deal = await channel.declare_exchange("ex.deal", aio_pika.ExchangeType.TOPIC, durable=True)
        ex_company = await channel.declare_exchange("ex.company", aio_pika.ExchangeType.TOPIC, durable=True)


        q_user_layout_created = await channel.declare_queue("q.layout.created.ai", durable=True)
        await q_user_layout_created.bind(ex_config, routing_key="layout.created")
        queues["user_layout_created"] = q_user_layout_created

        q_lead_field_updated = await channel.declare_queue("q.lead.field.updated.v2.ai", durable=True)
        await q_lead_field_updated.bind(ex_config, routing_key="lead.field.updated.v2")
        queues["lead_field_updated"] = q_lead_field_updated
        
        q_lead_picklist_value_updated = await channel.declare_queue("q.lead.picklist.value.updated.ai", durable=True)
        await q_lead_picklist_value_updated.bind(ex_config, routing_key="lead.picklist.value.updated")
        queues["lead_picklist_value_updated"] = q_lead_picklist_value_updated
        
        q_lead_new_field_created = await channel.declare_queue("q.config.lead.field.created.ai", durable=True)
        await q_lead_new_field_created.bind(ex_config, routing_key="config.lead.field.created")
        queues["lead_new_field_created"] = q_lead_new_field_created
        
        # Deal with other queues as needed
        
        q_deal_field_updated = await channel.declare_queue("q.deal.field.updated.v2.ai", durable=True)
        await q_deal_field_updated.bind(ex_deal, routing_key="deal.field.updated.v2")
        queues["deal_field_updated"] = q_deal_field_updated
        
        q_deal_new_field_created = await channel.declare_queue("q.deal.field.created.ai", durable=True)
        await q_deal_new_field_created.bind(ex_deal, routing_key="deal.field.created")
        queues["deal_new_field_created"] = q_deal_new_field_created
        
        q_new_user_deal_layout_created = await channel.declare_queue("q.deal.layout.created.ai", durable=True)
        await q_new_user_deal_layout_created.bind(ex_deal, routing_key="deal.layout.created")
        queues["new_user_deal_layout_created"] = q_new_user_deal_layout_created

        q_deal_picklist_value_updated = await channel.declare_queue("q.deal.picklist.value.updated.ai", durable=True)
        await q_deal_picklist_value_updated.bind(ex_deal, routing_key="deal.picklist.value.updated")
        queues["deal_picklist_value_updated"] = q_deal_picklist_value_updated

        # Company events

        q_company_field_updated = await channel.declare_queue(
            "q.company.field.updated.v2.ai", durable=True)
        await q_company_field_updated.bind(ex_company,
                                        routing_key="company.field.updated.v2")
        queues["company_field_updated"] = q_company_field_updated

        q_company_new_field_created = await channel.declare_queue(
            "q.company.field.created.ai", durable=True)
        await q_company_new_field_created.bind(ex_company,
                                            routing_key="company.field.created")
        queues["company_new_field_created"] = q_company_new_field_created

        q_new_user_company_layout_created = await channel.declare_queue(
            "q.company.layout.created.ai", durable=True)
        await q_new_user_company_layout_created.bind(ex_company,
                                                  routing_key="company.layout.created")
        queues["new_user_company_layout_created"] = q_new_user_company_layout_created

        q_company_picklist_value_updated = await channel.declare_queue(
            "q.company.picklist.value.updated.ai", durable=True)
        await q_company_picklist_value_updated.bind(ex_company,
                                                 routing_key="company.picklist.value.updated")
        queues["company_picklist_value_updated"] = q_company_picklist_value_updated

        # Handling Contact events

        q_new_user_contact_layout_created = await channel.declare_queue(
            "q.contact.layout.created.ai", durable=True)
        await q_new_user_contact_layout_created.bind(ex_config,
                                                     routing_key="layout.created")
        queues[
            "new_user_contact_layout_created"] = q_new_user_contact_layout_created

        q_contact_new_field_created = await channel.declare_queue(
            "q.config.contact.field.created.ai", durable=True)
        await q_contact_new_field_created.bind(ex_config,
                                               routing_key="config.contact.field.created")
        queues["contact_new_field_created"] = q_contact_new_field_created

        q_contact_field_updated = await channel.declare_queue(
            "q.lead.contact.updated.v2.ai", durable=True)
        await q_contact_field_updated.bind(ex_config,
                                           routing_key="lead.contact.updated.v2")
        queues["contact_field_updated"] = q_contact_field_updated

        q_contact_picklist_value_updated = await channel.declare_queue(
            "q.contact.picklist.value.updated.ai", durable=True)
        await q_contact_picklist_value_updated.bind(ex_config,
                                                    routing_key="contact.picklist.value.updated")
        queues[
            "contact_picklist_value_updated"] = q_contact_picklist_value_updated
        
        
        logger.info("RabbitMQ setup completed!")

        return connection, channel, queues
    
    except Exception as e:
        logger.error(f"Error setting up RabbitMQ: {e}")
        raise
