import asyncio
import json
import logging

import aio_pika
from app.rabbitmq.config import setup_rabbitmq
from app.services.listing_ai_services.processing_user_filters import (
    handle_new_field_created,
    preprocess_filters,
    process_filter_updated,
)
from app.services.listing_ai_services.vector_db_operations.elastic_vector_db_operations import \
    update_filter_when_lead_picklist_value_changed_in_es

from app.services.logger_config import logger


async def handle_event(routing_key: str, payload: str):
    if routing_key == "iam.user.created":
        print("Handle user created event")
    elif routing_key == "auth.login":
        print("Handle user login event")
    else:
        print(f"Unhandled event: {routing_key}")


async def consume_messages(queue: aio_pika.Queue):
    """Consume messages from RabbitMQ queue."""
    async with queue.iterator() as queue_iter:
        async for message in queue_iter:
            async with message.process():
                payload = message.body.decode()
                routing_key = message.routing_key
                print(f"Received message: {payload}, routing_key: {routing_key}")

                await handle_event(routing_key, payload)


async def start_rabbitmq_listener(app):
    """Setup and start the RabbitMQ listener."""
    connection, channel, queue = await setup_rabbitmq()

    # Store objects in FastAPI state
    app.state.rabbitmq_connection = connection
    app.state.rabbitmq_channel = channel
    app.state.rabbitmq_queue = queue

    # Start listening for messages
    app.state.rabbitmq_task = asyncio.create_task(consume_messages(queue))


async def stop_rabbitmq_listener(app):
    """Cleanup RabbitMQ connection on shutdown."""
    if app.state.rabbitmq_task:
        app.state.rabbitmq_task.cancel()
        await app.state.rabbitmq_task
        logger.info("RabbitMQ Consumer Task Stopped.")

    if app.state.rabbitmq_connection:
        await app.state.rabbitmq_connection.close()
        logger.info("RabbitMQ Connection Closed.")


async def consume_user_layout_created(queue, entity_type):
    try:
        async with queue.iterator() as queue_iter:
            async for message in queue_iter:
                async with message.process():
                    layout_data = message.body.decode()
                    layout_data = json.loads(layout_data)

                    logger.info(
                        f"Received message of consume_user_layout_created : {message.body.decode()}"
                    )
                    
                    tenant_id = str(layout_data["tenantId"])

                    preprocessed_filters = preprocess_filters(
                        layout_data, tenant_id, entity_type
                    )

                    insert_data_in_vectordb(
                        namespace=str(tenant_id),
                        entity_type=entity_type,
                        data=preprocessed_filters,
                    )

                    logger.info(
                        f"Inserted layout data in vectordb for tenant {tenant_id} and entity type {entity_type}."
                    )

    except Exception as e:
        logger.error(f"Error in consume_user_layout_created: {e}")


async def consume_field_updated(queue, entity_type):
    try:
        async with queue.iterator() as queue_iter:
            async for message in queue_iter:
                async with message.process():
                    logger.info(
                        f"Received message of consume_lead_field_updated : {message.body.decode()}"
                    )
                    received_data = message.body.decode()
                    received_data = json.loads(received_data)
                    process_filter_updated(received_data)
    except Exception as e:
        logger.error(f"Error in consume_field_updated: {e}")


async def consume_picklist_value_updated(queue, entity_type):
    try:
        async with queue.iterator() as queue_iter:
            async for message in queue_iter:
                async with message.process():
                    received_data = message.body.decode()
                    received_data = json.loads(received_data)

                    logger.info(
                        f"Received message after json load picklist_value_updated : {received_data}"
                    )
                    if (
                        received_data["newEntity"]["displayName"]
                        != received_data["oldEntity"]["displayName"]
                    ):
                        filter_id = (
                            str(received_data["metadata"]["tenantId"])
                            + "-"
                            + entity_type
                            + "-"
                            + received_data["newEntity"]["fieldName"]
                            + "-"
                            + str(received_data["newEntity"]["id"])
                        )
                        update_filter_when_lead_picklist_value_changed_in_es(
                            namespace=received_data["metadata"]["tenantId"],
                            entity_type=entity_type,
                            filter_id=filter_id,
                            new_display_name=received_data["newEntity"]["fieldDisplayName"] + " "+ received_data["newEntity"]["displayName"]
                        )
                        logger.info(f"Updated filter {filter_id} successfully.")
                    else:
                        logger.info(
                            f"No changes in display name for filter {filter_id}."
                        )
    except Exception as e:
        logger.error(f"Error in consume_picklist_value_updated: {e}")
        
        
async def consume_deal_or_company_picklist_value_updated(queue, entity_type):
    try:
        async with queue.iterator() as queue_iter:
            async for message in queue_iter:
                async with message.process():
                    received_data = message.body.decode()
                    received_data = json.loads(received_data)

                    logger.info(
                        f"Received message after json load consume_deal_or_company_picklist_value_updated : {received_data}"
                    )
                    if (
                        received_data["entity"]["displayName"]
                        != received_data["oldEntity"]["displayName"]
                    ):
                        filter_id = (
                            str(received_data["metadata"]["tenantId"])
                            + "-"
                            + entity_type
                            + "-"
                            + received_data["entity"]["fieldName"]
                            + "-"
                            + str(received_data["entity"]["id"])
                        )
                        update_filter_when_lead_picklist_value_changed_in_es(
                            namespace=received_data["metadata"]["tenantId"],
                            entity_type=entity_type,
                            filter_id=filter_id,
                            new_display_name=received_data["entity"]["fieldDisplayName"] + " "+ received_data["entity"]["displayName"]
                        )
                        logger.info(f"Updated filter {filter_id} successfully.")
                    else:
                        logger.info(
                            f"No changes in display name for filter {filter_id}."
                        )
    except Exception as e:
        logger.error(f"Error in consume_deal_or_company_picklist_value_updated: {e}")


 
async def consume_contact_field_updated(queue):
    try:
        async with queue.iterator() as queue_iter:
            async for message in queue_iter:
                async with message.process():
                    logger.info(
                        f"Received message of consume_contact_field_updated : {message.body.decode()}"
                    )
                    received_data = message.body.decode()
                    received_data = json.loads(received_data)
                    process_filter_updated(received_data)

    except Exception as e:
        logger.error(f"Error in consume_contact_field_updated: {e}")
        
        
async def consume_new_field_created(queue, entity_type):
    try:
        async with queue.iterator() as queue_iter:
            async for message in queue_iter:
                async with message.process():
                    logger.info(
                        f"Received message of consume_new_field_created : {message.body.decode()}"
                    )
                    received_data = message.body.decode()
                    received_data = json.loads(received_data)
                    handle_new_field_created(received_data, entity_type=entity_type)
                    
                    

    except Exception as e:
        logger.error(f"Error in consume_contact_field_updated: {e}")
