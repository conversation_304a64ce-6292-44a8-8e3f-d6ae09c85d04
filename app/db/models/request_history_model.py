# coding: utf-8
from sqlalchemy import Big<PERSON>nteger, <PERSON>olean, Column, DateTime, ForeignKey, \
  String, Text, text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from app.db.models.user_model import User  # Importing the User model
from .user_model import Base


class RequestsHistory(Base):
  __tablename__ = 'requests_history'

  id = Column(BigInteger, primary_key=True, nullable=False, server_default=text(
    "nextval('requests_history_id_seq'::regclass)"))
  user_id = Column(ForeignKey('users.id'), primary_key=True, nullable=False)
  tenant_id = Column(BigInteger, nullable=False)
  requested_input = Column(JSONB(astext_type=Text()), nullable=False)
  generated_response = Column(JSONB(astext_type=Text()), nullable=False)
  request_date = Column(DateTime, nullable=False)
  email_id = Column(BigInteger)
  input_token_consumed = Column(BigInteger, nullable=False)
  output_token_consumed = Column(BigInteger, nullable=False)
  total_consumed = Column(BigInteger, nullable=False)
  response_accepted_status = Column(Boolean, nullable=False)
  entity_id = Column(BigInteger)
  entity_type = Column(String)
  operation = Column(String)
  status = Column(String)

  user = relationship(User)
