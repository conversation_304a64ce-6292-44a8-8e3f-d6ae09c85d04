# coding: utf-8
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, Date, ForeignKey, text
from sqlalchemy.orm import relationship

from app.db.models.user_model import User  # Importing the User model
from .user_model import Base


class TokenHistory(Base):
  __tablename__ = 'token_history'

  id = Column(BigInteger, primary_key=True, nullable=False,
              server_default=text("nextval('token_history_id_seq'::regclass)"))
  user_id = Column(ForeignKey('users.id'), primary_key=True, nullable=False)
  tenant_id = Column(BigInteger, nullable=False)
  token_limit = Column(BigInteger, nullable=False)
  token_consumed = Column(BigInteger, nullable=False)
  usage_date = Column(Date, nullable=False, server_default=text(
    "(CURRENT_TIMESTAMP AT TIME ZONE 'UTC'::text)"))

  user = relationship(User)
