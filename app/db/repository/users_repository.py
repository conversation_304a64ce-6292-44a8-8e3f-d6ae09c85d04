from fastapi import HTTPException
from sqlalchemy.exc import SQLAlchemyError
from app.db.models.user_model import User
from app.services.logger_config import logger

class UserService:
    def __init__(self, session):
        self.session = session

    def insert_user(self, id, name, email, tenant_id):
        """Inserts a new user into the users table."""
        try:
            new_user = User(id=id, name=name, email=email, tenant_id=tenant_id)
            self.session.add(new_user)
            self.session.commit()
            return new_user

        except SQLAlchemyError as e:
            self.session.rollback()  
            logger.error(f"Database error in insert_user: {e}")
            raise HTTPException(
                status_code=400,
                detail="An unexpected error occurred while generating the email response",
            )

    def get_user_by_id(self, user_id):
        """Fetches a user by user_id."""
        try:
            return self.session.query(User).filter(User.id == user_id).first()

        except SQLAlchemyError as e:
            logger.error(f"Database error in get_user_by_id: {e}")
            raise HTTPException(
                status_code=400,
                detail="An unexpected error occurred while generating the email response",
            )
