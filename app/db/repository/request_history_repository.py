from fastapi import HTT<PERSON>Exception
from sqlalchemy import exists
from sqlalchemy.exc import SQLAlchemyError

from app.db.models.request_history_model import RequestsHistory
from app.exceptions import ResourceNotFoundException
from app.services.logger_config import logger


class RequestHistoryService:
    def __init__(self, session):
        self.session = session

    def insert_request(
        self,
        user_id,
        tenant_id,
        requested_input,
        generated_response,
        request_date,
        email_id,
        input_token_consumed,
        output_token_consumed,
        total_consumed,
        response_accepted_status,
        entity_id,
        entity_type,
        operation,
        status="COMPLETED",
    ):
        """Inserts a new request into the requests_history table."""
        try:
            new_request = RequestsHistory(
                user_id=user_id,
                tenant_id=tenant_id,
                requested_input=requested_input,
                generated_response=generated_response,
                request_date=request_date,
                email_id=email_id,
                input_token_consumed=input_token_consumed,
                output_token_consumed=output_token_consumed,
                total_consumed=total_consumed,
                response_accepted_status=response_accepted_status,
                entity_id=entity_id,
                entity_type=entity_type,
                operation=operation,
                status=status,
            )
            self.session.add(new_request)
            self.session.commit()
            return new_request
        except SQLAlchemyError as e:
            logger.error(f"Error inserting request: {e}", exc_info=True)
            self.session.rollback()
            raise Exception(f"Error inserting request: {e}")

    def update_request(
        self,
        request_id,
        generated_response=None,
        input_token_consumed=None,
        output_token_consumed=None,
        total_consumed=None,
        status="COMPLETED",
    ):
        """Updates an existing request in the requests_history table."""
        try:
            request = self.session.query(RequestsHistory).filter_by(
                id=request_id).first()
            if not request:
                raise Exception(f"Request with id {request_id} not found.")

            request.status = status
            if input_token_consumed is not None:
                request.input_token_consumed = input_token_consumed
            if generated_response is not None:
                request.generated_response = generated_response
            if output_token_consumed is not None:
                request.output_token_consumed = output_token_consumed
            if total_consumed is not None:
                request.total_consumed = total_consumed

            self.session.commit()
        except SQLAlchemyError as e:
            logger.error(f"Error updating request: {e}", exc_info=True)
            self.session.rollback()
            raise Exception(f"Error updating request: {e}")

    def update_response_status(self, request_id, status=False):
        """Updates the response_accepted_status of a request."""
        try:
            request = (
                self.session.query(RequestsHistory)
                .filter(RequestsHistory.id == request_id)
                .first()
            )
            if request:
                request.response_accepted_status = status
                self.session.commit()
                return request
            else:
                raise ResourceNotFoundException()
        except Exception as e:
            logger.error(f"Error updating request status: {e}")
            self.session.rollback()
            raise

    def get_request_data_by_id(self, request_id: int):
        """Fetch a request from the requests_history table by ID."""
        try:
            request_data = (
                self.session.query(RequestsHistory)
                .filter(RequestsHistory.id == request_id)
                .first()
            )
            if request_data:
                return {
                    "id": request_data.id,
                    "user_id": request_data.user_id,
                    "tenant_id": request_data.tenant_id,
                    "requested_input": request_data.requested_input,
                    "generated_response": request_data.generated_response,
                    "request_date": request_data.request_date,
                    "email_id": request_data.email_id,
                    "input_token_consumed": request_data.input_token_consumed,
                    "output_token_consumed": request_data.output_token_consumed,
                    "total_consumed": request_data.total_consumed,
                    "response_accepted_status": request_data.response_accepted_status,
                    "entity_id": request_data.entity_id,
                    "entity_type": request_data.entity_type,
                }
            else:
                raise HTTPException(
                status_code=400,
                detail="An unexpected error occurred while generating the email response",
            )
        except SQLAlchemyError as e:
            logger.error(f"Error fetching request data: {e}")
            raise HTTPException(
                status_code=400,
                detail="An unexpected error occurred while generating the email response",
            )

    def exists_status_for_call(self, call_id: int, status: str) -> bool:
        """
        Checks if there exists any request history record for the given call_id with the specified status.

        Args:
        call_id (str): The call ID to check.
        status (str): The status to check for (e.g., "IN_PROGRESS").

        Returns:
        bool: True if such a record exists, False otherwise.
        """
        return self.session.query(
            exists().where(
                (RequestsHistory.entity_id == call_id) &
                (RequestsHistory.status == status) &
                (RequestsHistory.entity_type == "call")
            )
        ).scalar()