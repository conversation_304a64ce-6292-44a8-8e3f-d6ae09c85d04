from fastapi import HTTPException
from sqlalchemy.exc import SQLAlchemyError
from app.db.models.token_history_model import TokenHistory
from app.services.logger_config import logger


class TokenHistoryService:
    def __init__(self, session):
        self.session = session

    def update_or_insert_token_history(
        self, user_id, tenant_id, token_limit, token_consumed, usage_date
    ):
        """Checks if the token history for the user and tenant exists for the particular day."""
        try:
            existing_entry = (
                self.session.query(TokenHistory)
                .filter(
                    TokenHistory.user_id == user_id,
                    TokenHistory.tenant_id == tenant_id,
                    TokenHistory.usage_date == usage_date,
                )
                .first()
            )

            new_token_entry = None  # Initialize variable
            if existing_entry:
                existing_entry.token_consumed += token_consumed
                new_token_entry = existing_entry
            else:
                new_token_entry = TokenHistory(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    token_limit=token_limit,
                    token_consumed=token_consumed,
                    usage_date=usage_date,
                )
                self.session.add(new_token_entry)

            self.session.commit()
            return new_token_entry

        except SQLAlchemyError as e:
            self.session.rollback()  # Rollback on failure
            logger.error(f"Database error in update_or_insert_token_history: {e}")
            raise HTTPException(
                status_code=400,
                detail="An unexpected error occurred while generating the email response",
            )

    def is_token_limit_exceeded(self, user_id, tenant_id, usage_date):
        """Checks if the token limit has been exceeded for a user and tenant on a given date."""
        try:
            token_record = (
                self.session.query(TokenHistory)
                .filter(
                    TokenHistory.user_id == user_id,
                    TokenHistory.tenant_id == tenant_id,
                    TokenHistory.usage_date == usage_date,
                )
                .first()
            )

            if token_record:
                return token_record.token_consumed >= token_record.token_limit
            return False

        except SQLAlchemyError as e:
            logger.error(f"Token limit is exceeded and still user is trying to access: {e}")
            raise HTTPException(
                status_code=400,
                detail="Token limit is exceeded and still user is trying to access",
            )
