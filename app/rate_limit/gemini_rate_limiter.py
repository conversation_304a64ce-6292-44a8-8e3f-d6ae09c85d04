import aioredis

from fastapi import Depends

from dotenv import load_dotenv

import os

import datetime




from app.exceptions import RateLimitExceededException

from app.db.repository.token_history_repository import TokenHistoryService

load_dotenv()




REDIS_HOST = os.getenv("REDIS_HOST")




# Redis connection pool

redis_pool = None




# Gemini API limits

GEMINI_MAX_GLOBAL_RPM = 10000  # Requests per minute

GEMINI_MAX_GLOBAL_TPM = 10000000  # Tokens per minute

GEMINI_NUM_TENANTS = 1  




GEMINI_MIN_RPM_PER_TENANT = 1  # Minimum guaranteed requests per tenant

GEMINI_MAX_RPM_PER_TENANT = 1  # Hard limit per tenant




# Lua script for atomic increment and TTL setting (reused from OpenAI logic)

INCREMENT_SCRIPT = """

local current = redis.call('INCR', KEYS[1])

if current == 1 then

    redis.call('EXPIRE', KEYS[1], ARGV[1])

end

return current

"""




async def get_redis():

  global redis_pool

  if not redis_pool:

    redis_pool = await aioredis.from_url(

        REDIS_HOST,

        decode_responses=True

    )

  return redis_pool




async def get_gemini_tenant_limit(redis, tenant_id: str) -> float:

    """

    Calculate the allowed requests per tenant for Gemini API.

    """

    global_used = int(await redis.get("gemini:global:used") or 0)




    # Calculate available global capacity

    remaining_global = GEMINI_MAX_GLOBAL_RPM - global_used

    dynamic_quota = max(0, remaining_global / GEMINI_NUM_TENANTS)




    # Calculate allowed requests for this tenant

    allowed = GEMINI_MIN_RPM_PER_TENANT + dynamic_quota

    return min(GEMINI_MAX_RPM_PER_TENANT, allowed)







async def gemini_rate_limiter(tenant_id: str, redis=Depends(get_redis), db_session=None):

    """

    Rate limiter for Gemini API.

    """

    # Initialize services

    token_history_service = TokenHistoryService(db_session)




    # Get current allowed limit before incrementing

    allowed_per_tenant = await get_gemini_tenant_limit(redis, tenant_id)




    # Prepare keys

    tenant_key = f"gemini:tenant:{tenant_id}:used"

    global_key = "gemini:global:used"




    # Atomic increment with TTL initialization

    async with redis.pipeline(transaction=True) as pipe:

        await pipe.eval(INCREMENT_SCRIPT, 1, tenant_key, 60)

        await pipe.eval(INCREMENT_SCRIPT, 1, global_key, 60)

        tenant_used, global_used = await pipe.execute()




    # Enforce limits

    if tenant_used > allowed_per_tenant:

        await redis.decr(tenant_key)  # Rollback tenant counter

        await redis.decr(global_key)  # Rollback global counter

        raise RateLimitExceededException(

            "Tenant rate limit exceeded. Please upgrade your plan."

        )




    if global_used > GEMINI_MAX_GLOBAL_RPM:

        await redis.decr(tenant_key)  # Rollback tenant counter

        await redis.decr(global_key)  # Rollback global counter

        raise RateLimitExceededException(

            "Tenant rate limit exceeded. Please upgrade your plan."

        )




    # Track token usage (example values, replace with actual token usage from Gemini API)

    input_tokens = 100  # Replace with actual input tokens consumed

    output_tokens = 200  # Replace with actual output tokens consumed

    total_tokens = input_tokens + output_tokens




    # Update token history

    token_history_service.update_or_insert_token_history(

        tenant_id=tenant_id,

        token_limit=GEMINI_MAX_GLOBAL_TPM,

        token_consumed=total_tokens,

        usage_date=datetime.now().date(),

    )




    return tenant_id