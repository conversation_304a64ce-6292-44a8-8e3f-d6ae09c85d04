import aioredis
from fastapi import Depends
from dotenv import load_dotenv
import os

from loguru import logger

from app.exceptions import RateLimitExceededException
load_dotenv()

REDIS_HOST = os.getenv("REDIS_HOST")

MAX_GLOBAL_RPM = 5000  # Requests per minute
MAX_GLOBAL_TPM = 2000000  # Tokens per minute
NUM_TENANTS = 200

MIN_RPM_PER_TENANT  = 1  # Minimum guaranteed requests per tenant
MAX_RPM_PER_TENANT = 15  # Hard limit per tenant


# Redis connection pool
redis_pool = None

# Lua script for atomic increment and TTL setting
INCREMENT_SCRIPT = """
local current = redis.call('INCR', KEYS[1])
if current == 1 then
    redis.call('EXPIRE', KEYS[1], ARGV[1])
end
return current
"""


async def get_redis():
  global redis_pool
  if not redis_pool:
    redis_pool = await aioredis.from_url(
        REDIS_HOST,
        decode_responses=True
    )
  return redis_pool


async def get_tenant_limit(redis, tenant_id: str) -> float:
  global_used = int(await redis.get("global:used") or 0)

  # Calculate available global capacity
  remaining_global = MAX_GLOBAL_RPM - global_used
  dynamic_quota = max(0, remaining_global / NUM_TENANTS)

  # Calculate allowed requests for this tenant
  allowed = MIN_RPM_PER_TENANT + dynamic_quota
  return min(MAX_RPM_PER_TENANT, allowed)


async def rate_limiter(tenant_id: str, redis=Depends(get_redis)):
  # Get current allowed limit before incrementing
  allowed_per_tenant = await get_tenant_limit(redis, tenant_id)

  # Prepare keys
  tenant_key = f"tenant:{tenant_id}:used"
  global_key = "global:used"

  # Atomic increment with TTL initialization
  async with redis.pipeline(transaction=True) as pipe:
    await pipe.eval(INCREMENT_SCRIPT, 1, tenant_key, 60)
    await pipe.eval(INCREMENT_SCRIPT, 1, global_key, 60)
    tenant_used, global_used = await pipe.execute()

  # Enforce limits
  if tenant_used > allowed_per_tenant:
    await redis.decr(tenant_key)  # Rollback tenant counter
    await redis.decr(global_key)  # Rollback global counter
    raise RateLimitExceededException(
        "Tenant rate limit exceeded. Please upgrade your plan."
    )

  if global_used > MAX_GLOBAL_RPM:
    await redis.decr(tenant_key)  # Rollback tenant counter
    await redis.decr(global_key)  # Rollback global counter
    raise RateLimitExceededException(
        "Tenant rate limit exceeded. Please upgrade your plan."
    )

  return tenant_id