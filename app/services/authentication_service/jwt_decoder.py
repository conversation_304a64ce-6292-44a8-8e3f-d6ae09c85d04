import os
from typing import <PERSON><PERSON>

import jwt
from dotenv import load_dotenv
from fastapi import HTTPException

from app.exceptions import AuthenticationError

load_dotenv()
from app.services.logger_config import logger

SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = os.getenv("ALGORITHM")


def decode_token(token: str):
  try:
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    return payload
  except jwt.ExpiredSignatureError:
    logger.error(
        f"Authentication Error: Token has expired. - error_type=TokenExpired"
    )
    raise AuthenticationError(message="Token has expired.")
  except jwt.InvalidTokenError:
    logger.error(
        f"Authentication Error: Invalid token. - error_type=InvalidToken"
    )
    raise AuthenticationError(message="Invalid JWT token.")


def validate_user_request(token: str) -> Tuple[str, str, str]:
    try:
        payload = decode_token(token)

        source_type = payload["data"].get("source", {}).get("type", "")
        if source_type not in ["Web", "Mobile"]:
            raise HTTPException(
                status_code=403,
                detail=f"Unsupported source type: {source_type}"
            )

        ai_service = next(
            (p for p in payload["data"]["permissions"] if p["name"] == "ai"),
            None
        )

        if not ai_service:
            raise HTTPException(
                status_code=403,
                detail="User does not have AI permissions"
            )

        user_id = payload["data"]["userId"]
        tenant_id = payload["data"]["tenantId"]
        username = payload["data"]["username"]

        return tenant_id, user_id, username

    except AuthenticationError as auth_exc:
        logger.warning(f"Authentication failed: {str(auth_exc)}")
        raise auth_exc

    except Exception as e:
        logger.error(f"Unexpected error in validate_user_request - Error: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail="Something didn't work as expected"
        )
