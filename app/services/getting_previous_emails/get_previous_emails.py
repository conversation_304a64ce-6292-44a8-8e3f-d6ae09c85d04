import os

import requests
from fastapi import HTTPException  # Only if using FastAPI

from app.services.getting_previous_emails.calculate_tokens import \
  extract_last_communications
from app.services.logger_config import logger

def get_previous_emails_content(email_id, size, token):
  base_url = os.getenv("EMAIL_BASE_PATH")

  if not base_url:
    logger.error("EMAIL_BASE_PATH environment variable is not set")
    raise ValueError("EMAIL_BASE_PATH environment variable is missing")

  full_url = f"{base_url}/v1/emails/{email_id}/history"

  params = {
    "page": 1,
    "size": size,
    "view": "text-body"
  }
  headers = {
    "Authorization": f"Bearer {token}"
  }

  try:
    response = requests.get(full_url, params=params, headers=headers)
    response.raise_for_status()  # Raises error for HTTP errors (4xx, 5xx)
    return extract_last_communications(
        response.json())  # Ensure this function is defined

  except requests.exceptions.RequestException as e:
    logger.error(
        f"Error occurred while getting previous mails for reply: {str(e)}"
    )
    raise HTTPException(status_code=400,
                        detail="Error occurred while getting previous mails for reply.")  # If using FastAPI
