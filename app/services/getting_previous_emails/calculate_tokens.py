import os
import tiktoken
from dotenv import load_dotenv
from fastapi import HTTPException
from app.services.logger_config import logger

load_dotenv()

prev_email_retrival_token_limit = int(os.getenv("PREV_EMAIL_RETRIEVAL_TOKEN_LIMIT", "2000"))

encoding = tiktoken.get_encoding("cl100k_base")


def count_tokens(text):
    tokens = encoding.encode(text)
    return len(tokens)


def summarize_text(text, target_token_limit):

    logger.info(f"Starting summarization. Initial token count: {count_tokens(text)}")

    sentences = text.split(". ")
    summary = ". ".join(sentences[:2]) + "... " + ". ".join(sentences[-2:])

    while count_tokens(summary) > target_token_limit:
        summary = ". ".join(sentences[:len(sentences) // 2])
        sentences = summary.split(". ")

    logger.info(f"Final summary token count: {count_tokens(summary)}")
    return summary


def extract_last_communications(data):
    try:
        emails = data.get("content", [])
        if not emails:
            return {"content": []}

        email_bodies = [email["body"] for email in emails]

        # Start with the last 3 emails
        last_three_index = max(len(email_bodies) - 3, 0)
        extracted_bodies = email_bodies[last_three_index:]

        token_count = count_tokens(str({"content": extracted_bodies}))
        logger.info(f"Token count after extracting last 3 emails: {token_count}")

        # If last 3 emails exceed token limit, summarize immediately
        if token_count > prev_email_retrival_token_limit:
            summarized_content = summarize_text(str({"content": extracted_bodies}), prev_email_retrival_token_limit)
            return {"content": summarized_content}

        # Try adding one more previous email
        current_position = last_three_index - 1

        while current_position >= 0:
            new_bodies = [email_bodies[current_position]] + extracted_bodies
            new_token_count = count_tokens(str({"content": new_bodies}))

            logger.info(f"Trying to add email at index {current_position}, new token count: {new_token_count}")

            if new_token_count > prev_email_retrival_token_limit:
                logger.info("Exceeded token limit. Summarizing...")
                summarized_content = summarize_text(str({"content": new_bodies}), prev_email_retrival_token_limit)
                return {"content": summarized_content}

            extracted_bodies = new_bodies
            token_count = new_token_count
            current_position -= 1

        logger.info("Returning extracted emails without summarization.")
        return {"content": extracted_bodies}

    except Exception as e:
        logger.error(f"Unexpected error in extract_last_communications - Error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")
