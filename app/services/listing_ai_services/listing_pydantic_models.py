from typing import List, Optional, Dict, Any, Union

from pydantic import BaseModel, EmailStr

class ListingAiRequestFormat(BaseModel):
  userQuery: str
  


class Rule(BaseModel):
    operator: str
    id: str
    field: str
    type: str
    value: Union[str, int, List[str], List[int], None]  # Updated to support list of integers
    lookupUrl: Optional[str] = None
    timeZone: Optional[str] = None
    primaryField: Optional[str] = None
    property: Optional[str] = None
    relatedFieldIds: Optional[List[str]] = None


class JsonRule(BaseModel):
    rules: List[Rule]
    condition: str
    valid: bool


class ListingAiResponseFormat(BaseModel):
    jsonRule: JsonRule  
    entityType: str
    

class ListingAiAPIResponseFormat(BaseModel):
    jsonRule: Optional[JsonRule]
    entityType: str
    operation: str
  