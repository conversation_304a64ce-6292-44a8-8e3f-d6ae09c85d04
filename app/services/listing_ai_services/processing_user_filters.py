
import json
import logging
from typing import Any, Dict, List

from fastapi import HTT<PERSON>Exception

from app.services.listing_ai_services.vector_db_operations.elastic_vector_db_operations import \
  update_filter_in_es, get_filter_id_by_prefix_es, \
  update_multiple_filters_in_vectordb_es, bulk_index_filters
from app.services.logger_config import logger


type_mapping = {
    "TEXT_FIELD": "string",
    "NUMBER": "double",
    "URL": "string",
    "CHECKBOX": "boolean",
    "EMAIL": "string",
    "PHONE": "string",
    "TOGGLE": "boolean",
    "FORECASTING_TYPE": "string",
    "PARAGRAPH_TEXT": "string",
}

picklist_values_key_map = {
    "deal": "picklistValues",
    "company": "pickListValues",
    "lead": "values",
    "contact": "values",
}

operator_mapping = {
    "TEXT_FIELD": "equal / not_equal / contains / not_contains / in / not_in / is_empty / is_not_empty / begins_with",
    "PARAGRAPH_TEXT": "equal / not_equal / contains / not_contains / in / not_in / is_empty / is_not_empty / begins_with",
    "NUMBER": "equal / not_equal / greater / greater_or_equal / less / less_or_equal / between / not_between / in / not_in / is_null / is_not_null",
    "URL": "equal / not_equal / contains / not_contains / in / not_in / is_empty / is_not_empty / begins_with",
    "CHECKBOX": "equal / not_equal",
    "PICK_LIST": "equal / not_equal / is_not_null / is_null / in / not_in",
    "MULTI_PICKLIST": "equal / not_equal / is_not_null / is_null / in / not_in",
    "DATETIME_PICKER": "greater / greater_or_equal / less / less_or_equal / between / not_between / is_not_null / is_null / today / yesterday / tomorrow / last_seven_days / next_seven_days / last_fifteen_days / next_fifteen_days / last_thirty_days / next_thirty_days / week_to_date / current_week / last_week / next_week / month_to_date / current_month / last_month / next_month / quarter_to_date / current_quarter / last_quarter / next_quarter / year_to_date / current_year / last_year / next_year / before_current_date_and_time / after_current_date_and_time",
    "EMAIL": "equal / not_equal / contains / not_contains / in / not_in / is_empty / is_not_empty / begins_with",
    "PHONE": "equal / not_equal / contains / not_contains / in / not_in / is_empty / is_not_empty / begins_with",
    "TOGGLE": "equal / not_equal",
    "FORECASTING_TYPE": "equal / not_equal / in / not_in / is_empty / is_not_empty",
    "ENTITY_FIELDS": "equal / not_equal / in / not_in / is_not_null / is_null",
    "LOOK_UP": "equal / not_equal / is_not_null / is_null / in / not_in",
    "PIPELINE_STAGE": "equal / not_equal / in / not_in",
    "PIPELINE": "equal / not_equal / is_not_null / is_null / in / not_in"
}

static_options = {
    "TOGGLE": "true / false",
    "FORECASTING_TYPE": "OPEN / CLOSED_WON / CLOSED_UNQUALIFIED / CLOSED_LOST",
}

standard_picklist = {
    "requirementCurrency",
    "country",
    "timezone",
    "companyCountry",
    "companyIndustry",
}


def preprocess_filters(data, tenant_id, entity_type):
    try:
        columns = data["event"]["pageConfig"]["tableConfig"]["columns"]
        output = []

        for col in columns:
            field_type = col.get("fieldType")
            col_id = col.get("id")
            header = col.get("header")
            is_filterable = col.get("isFilterable", False)

            if (
                field_type in ("PICK_LIST", "MULTI_PICKLIST")
                and isinstance(col.get("picklist"), dict)
                and "picklistValues" in col["picklist"]
            ):
                field = (
                    col_id
                    if field_type == "PICK_LIST"
                    else f"customFieldValues.{col_id}"
                )
                value_type = "string" if col_id in standard_picklist else "long"

                for item in col["picklist"]["picklistValues"]:
                    value = item["name"] if col_id in standard_picklist else item["id"]
                    output.append(
                        {
                            "filter_id": tenant_id +"-"+ entity_type +"-"+ col_id +"-"+ str(item["id"]),
                            "operator": operator_mapping[field_type],
                            "id": col_id,
                            "field": field,
                            "type": value_type,
                            "value": value,
                            "relatedFieldIds": "null",
                            "isFilterable": is_filterable,
                            "header": f"{header} is {item['displayName']}",
                        }
                    )

            elif field_type == "DATETIME_PICKER":
                output.append(
                    {
                        "filter_id": tenant_id +"-"+ entity_type +"-"+ col_id,
                        "operator": operator_mapping[field_type],
                        "id": col_id,
                        "field": col_id,
                        "type": "date",
                        "value": "It should be in ISO 8601 format example 2022-07-13T11:16:45.876+0000",
                        "relatedFieldIds": "null",
                        "isFilterable": is_filterable,
                        "timeZone": "Asia/Calcutta",
                        "header": header,
                    }
                )

            elif field_type == "ENTITY_FIELDS":
                output.append(
                    {
                        "filter_id": tenant_id +"-"+ entity_type +"-"+ col_id,
                        "operator": operator_mapping[field_type],
                        "id": col_id,
                        "field": col_id,
                        "primaryField": col["primaryField"],
                        "property": "teams",
                        "type": "long",
                        "value": "",
                        "relatedFieldIds": "null",
                        "isFilterable": is_filterable,
                        "header": header,
                    }
                )

            elif field_type == "LOOK_UP":
                output.append(
                    {
                        "filter_id": tenant_id +"-"+ entity_type +"-"+ col_id,
                        "operator": operator_mapping[field_type],
                        "id": col_id,
                        "field": col_id,
                        "type": "long",
                        "value": "",
                        "relatedFieldIds": "null",
                        "isFilterable": is_filterable,
                        "lookupUrl": col["lookup"]["lookupUrl"],
                        "header": header,
                    }
                )

            elif field_type == "PIPELINE":
                output.append(
                    {
                      "filter_id": tenant_id + "-" + entity_type + "-" + col_id,
                      "operator": operator_mapping[field_type],
                      "id": col_id,
                      "field": col_id,
                      "type": "long",
                      "value": "",
                      "isFilterable": is_filterable,
                      "lookupUrl": col["lookup"]["lookupUrl"],
                      "header": header,
                    }
                )
            elif field_type == "PIPELINE_STAGE":
                output.append(
                    {
                      "filter_id": tenant_id + "-" + entity_type + "-" + col_id,
                      "operator": operator_mapping[field_type],
                      "id": col_id,
                      "field": col_id,
                      "type": "long",
                      "value": "",
                      "relatedFieldIds": col.get("relatedFieldIds", "null"),
                      "isFilterable": is_filterable,
                      "lookupUrl": col["lookup"]["lookupUrl"],
                      "header": header,
                    }
                )

            elif field_type in type_mapping:
                output.append(
                    {
                        "filter_id": tenant_id +"-"+ entity_type +"-"+ col_id,
                        "operator": operator_mapping[field_type],
                        "id": col_id,
                        "field": col_id,
                        "type": type_mapping[field_type],
                        "value": static_options.get(field_type, ""),
                        "relatedFieldIds": "null",
                        "isFilterable": is_filterable,
                        "header": header,
                    }
                )

        return output
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail="Error processing filters: preprocess_filters " + str(e),
        )
        

def handle_new_field_created(received_data: Dict[str, Any], entity_type: str):
    try:
        logger.info(
            f"Received data in handle_new_field_created: {received_data}")
        
        output = []

        field_type_key = "type" if entity_type in ("lead", "contact") else "fieldType"
        picklist_value_key = picklist_values_key_map.get(entity_type.lower(), "picklistValues")


        if received_data["fields"][0][field_type_key] in ["PICK_LIST", "MULTI_PICKLIST"]:
            field = (
                received_data["fields"][0]["name"]
                if received_data["fields"][0][field_type_key] == "PICK_LIST"
                else f"customFieldValues.{received_data['fields'][0]['name']}"
            )
            value_type = "string" if received_data["fields"][0]["name"] in standard_picklist else "long"

            for item in received_data["fields"][0]["picklist"][picklist_value_key]:
                value = item["name"] if received_data["fields"][0]["name"] in standard_picklist else item["id"]
                output.append(
                    {
                        "filter_id": str(received_data["tenantId"]) +"-"+ entity_type +"-"+ received_data["fields"][0]["name"] +"-"+ str(item["id"]),
                        "operator": operator_mapping[received_data["fields"][0][field_type_key]],
                        "id": received_data["fields"][0]["name"],
                        "field": field,
                        "type": value_type,
                        "value": value,
                        "relatedFieldIds": "null",
                        "isFilterable": received_data["fields"][0]["filterable"],
                      "header": f"{received_data['fields'][0]['displayName']} {item['displayName']}".lower(),
                    }
                )


            bulk_index_filters(output, tenant_id=str(received_data["tenantId"]), alias_name=str(received_data["tenantId"])+ "-" + entity_type, entity_type=entity_type)
            logger.info(
                f"successfully inserted filter with display name {received_data['fields'][0]['displayName']}")
        else:
            output.append(
                {
                    "filter_id": str(received_data["tenantId"]) +"-"+ entity_type +"-"+ received_data["fields"][0]["name"],
                    "operator": operator_mapping[received_data["fields"][0][field_type_key]],
                    "id": received_data["fields"][0]["name"],
                    "field": received_data["fields"][0]["name"],
                    "type": type_mapping[received_data["fields"][0][field_type_key]],
                    "value": static_options.get(received_data["fields"][0][field_type_key], ""),
                    "relatedFieldIds": "null",
                    "isFilterable": received_data["fields"][0]["filterable"],
                    "header": received_data["fields"][0]["displayName"].lower(),
                }
            )
            bulk_index_filters(output, tenant_id=str(received_data["tenantId"]), alias_name=str(received_data["tenantId"])+ "-" + entity_type, entity_type=entity_type)
            logger.info(
                f"successfully inserted filter with display name {received_data['fields'][0]['displayName']}")
    except Exception as e:
        logger.error(
            f"Error in handle_new_field_created: {e}",
            extra={"received_data": received_data},
        )

        raise HTTPException(
            status_code=400,
            detail="Error processing filters: handle_new_field_created " + str(e),
        )
        
        
        
        
def process_filter_updated(received_data: Dict[str, Any]):
    """ Consume contact field updated event from RabbitMQ and update the vector database.
    Args:
        received_data (dict): The data received from the RabbitMQ message.
    """
    try:
        logger.info(
            f"Received data in process_filter_updated: {received_data}"
        )
        # handling picklist filters
        
        if (
            (received_data["entity"]["displayName"]
            != received_data["oldEntity"]["displayName"]) or (received_data["entity"]["filterable"]
            != received_data["oldEntity"]["filterable"])
        ):
            entityType = received_data["metadata"]["entityType"].lower()
            tenantId = str(received_data["metadata"]["tenantId"])
            namespace = received_data["metadata"]["tenantId"]

            if received_data["entity"]["fieldType"] in ["PICK_LIST", "MULTI_PICKLIST"]:
                prefix_filter_id = (
                    tenantId + "-" + entityType + "-" + received_data["entity"]["name"]
                )

                logger.info(
                    f"Prefix filter ID: {prefix_filter_id} for entity type: {entityType}"
                )
                existing_picklist_values_ids=get_filter_id_by_prefix_es(alias_name=tenantId + "-" + entityType, prefix_filter_id=prefix_filter_id)
                logger.info(
                    f"Existing picklist values IDs: {existing_picklist_values_ids}"
                )
                update_multiple_filters_in_vectordb_es(
                    namespace=namespace,
                    entity_type=entityType,
                    filter_ids=existing_picklist_values_ids,
                    new_display_name=received_data["entity"]["displayName"].lower(),
                    isFilterable=received_data["entity"]["filterable"],
                )

            else:
                # handling non-picklist filters
                filter_id = (
                    tenantId + "-" + entityType + "-" + received_data["entity"]["name"]
                )

                alias_name = tenantId + "-" + entityType
                logger.info(
                    f"Updating filter with ID: {filter_id} and alias: {alias_name} and display name: {received_data['entity']['displayName']}")
                update_filter_in_es(
                    alias_name=alias_name,
                    entity_type=entityType,
                    filter_id=filter_id,
                    new_display_name=received_data["entity"]["displayName"].lower(),
                    is_filterable=received_data["entity"]["filterable"],
                )
        else:
            logger.info(
                f"No changes in display name for filter ."
            )
            
        logger.info(
            f"Successfully updated filter with display name {received_data['entity']['displayName']}"
        )
    except Exception as e:
        logger.error(
            f"Error in process_filter_updated: {e}",
            extra={"received_data": received_data},
        )
        # Raise an HTTPException with a 400 status code and the error message
        raise HTTPException(
            status_code=400,
            detail="Error processing filters: process_filter_updated " + str(e),
        )
