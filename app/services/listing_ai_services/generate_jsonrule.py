import json
import os
import openai
from typing import Dict
from datetime import date, datetime, timezone
from time import perf_counter
from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS
import re

from dotenv import load_dotenv
from fastapi import HTTPException
from openai import OpenAI, RateLimitError
from sqlalchemy.orm import Session

from app.db.repository.request_history_repository import RequestHistoryService
from app.db.repository.token_history_repository import TokenHistoryService
from app.exceptions import TokenLimitExceededException, \
  NamespaceNotFoundException, FiltersNotFoundException, \
  RateLimitExceededException
from app.services.listing_ai_services.listing_pydantic_models import (
  ListingAiRequestFormat,
)
from app.services.listing_ai_services.llm_calling import generate_llm_filters, \
  select_valid_pipeline_stage, select_relevant_user, select_relevant_pipeline, \
  select_relevant_product, select_relevant_company, select_relevant_contact, \
  generate_llm_filters_company
from app.services.listing_ai_services.lookup_api_calls.entity_field_api_call import (
  entity_field_data_extractor, get_entity_labels,
)
from app.services.listing_ai_services.lookup_api_calls.lookup_field_api_call import (
  lookup_user, get_pipeline_stages, get_pipeline, lookup_products,
  lookup_companies, lookup_contacts,
)
from app.services.listing_ai_services.lookup_api_calls.time_zone_api_call import \
  get_time_zone
from app.services.listing_ai_services.vector_db_operations.elastic_vector_db_operations import \
  get_embedding, \
  search_similar_documents_in_standard_picklist
from app.services.logger_config import logger

load_dotenv()

daily_token_limit = os.getenv("DAILY_TOKEN_LIMIT")
openai_api_key = os.getenv("OPENAI_API_KEY")

client = OpenAI(api_key=openai_api_key)

ENTITY_FIELDS = [
  "ownerFields",
  "createdByFields",
  "updatedByFields",
  "importedByFields",
  "convertedByFields",
]

LOOK_UP_FIELDS = [
  "company",
  "products",
  "associatedContacts",
  "ownerId",
  "ownedBy",
  "convertedBy",
  "createdBy",
  "updatedBy",
  "importedBy",
]

CUSTOM_STOP_WORDS = {
    'the', 'is', 'at', 'which', 'on', 'and', 'a', 'an', 'of', 'in', 'to',
    'for', 'with', 'by', 'as', 'that', 'this', 'was', 'were', 'been', 'be',
    'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'can', 'could',
}

def clean_user_query(query: str) -> str:
    # Lowercase and strip
    query = query.lower().strip()

    # Tokenize using regex (only keep words)
    tokens = re.findall(r'\b\w+\b', query)

    # Remove custom stopwords
    filtered = [token for token in tokens if token not in CUSTOM_STOP_WORDS]

    return ' '.join(filtered)


def get_filtered_entity_labels(token: str) -> Dict[str, Dict[str, str]]:
  entity_labels = get_entity_labels(bearer_token=token)
  return {
    k: v for k, v in entity_labels.items()
    if k in {"LEAD", "DEAL", "CONTACT", "COMPANY"}
  }


def identify_single_entity_from_query(user_query: str, token: str) -> str:
  filtered_labels = get_filtered_entity_labels(token)

  system_prompt = (
      "You are an assistant that extracts the most relevant single entity type from a user query. "
      "Valid entities are:\n" +
      "\n".join(
          [f"- {key} (displayed as '{val['displayName']}')" for key, val in
           filtered_labels.items()]) +
      "\n\nReturn only the entity key (e.g., lead, deal, contact, company) in lowercase. "
      "Do not return anything else. Only one word."
  )

  try:

    response = client.chat.completions.create(
        messages=[
          {"role": "system", "content": system_prompt},
          {"role": "user", "content": f"Query: {user_query}"}
        ],
        model="gpt-4o",
        temperature=0
    )

    entity = response.choices[0].message.content.strip().lower()

    # Ensure it's one of the valid keys (as lowercase)
    valid_keys = {k.lower() for k in filtered_labels.keys()}
    if entity not in valid_keys:
      logger.info(
          f"Extracted entity '{entity}' not in valid keys: {valid_keys}")
      return ""
    return entity

  except Exception as e:
    logger.error(f"Failed to extract entity from query: {e}")
    return ""


def generate_filters(
    user_query: str,
    namespace: str,
    db_session: Session,
    tenant_id: str,
    user_id: str,
    request: ListingAiRequestFormat,
    token: str,
):
  try:
    request_history_service = RequestHistoryService(db_session)
    token_history_service = TokenHistoryService(db_session)


    entity=identify_single_entity_from_query(user_query=user_query,token=token)
    logger.info(f"Identified entity from user query - {entity}")

    parts = re.split(r"\s+and\s+", user_query.lower())
    retrieved_filters_standard_picklist=run_multi_vector_search(parts, int(tenant_id), entity)

  # Check if token limit exceeded
  #   retrieved_filters_standard_picklist=search_similar_documents_in_standard_picklist(get_embedding(user_query),int(tenant_id), entity)
    logger.info(f"Retrieved filters from standard picklist - {retrieved_filters_standard_picklist}")

    if entity == "company":
      input_token, output_token, total_token_consumed, generated_filter = generate_llm_filters_company(
          data=retrieved_filters_standard_picklist,
          user_query=user_query,
          entity_type=entity
      )
    else:
      input_token, output_token, total_token_consumed, generated_filter = generate_llm_filters(
          data=retrieved_filters_standard_picklist,
          user_query=user_query,
          entity_type=entity
      )

    generated_filter = generated_filter.model_dump()

    logger.info(f"Generated filter from LLM - {generated_filter}")

    entity_type = generated_filter.get("entityType")

    token_history_service.update_or_insert_token_history(
        user_id=user_id,
        tenant_id=tenant_id,
        token_limit=daily_token_limit,
        token_consumed=total_token_consumed,
        usage_date=date.today(),
    )

    # checking filter for lookup fields
    if "jsonRule" in generated_filter and "rules" in generated_filter[
      "jsonRule"]:
      for rule in generated_filter["jsonRule"]["rules"]:
        logger.info(f"Processing rule: {rule}")
        if isinstance(rule, dict) and rule.get("id") in ENTITY_FIELDS:
          try:
            entity_result = entity_field_data_extractor(rule["value"],
                                                        bearer_token=token)
            if entity_result and "content" in entity_result and entity_result[
              "content"]:
              if len(entity_result["content"]) > 0:
                rule["value"] = entity_result["content"][0]["id"]
              else:
                logger.info(
                  f"Empty content list from entity_field_data_extractor for value: {rule['value']}")
                continue
            else:
              logger.info(
                f"Invalid response from entity_field_data_extractor for value: {rule['value']}")
              continue
          except (IndexError, KeyError) as e:
            logger.info(
              f"Error accessing entity field data: {str(e)} for value: {rule['value']}")
            continue

        elif isinstance(rule, dict) and rule.get("id") in LOOK_UP_FIELDS:
          try:
            if rule.get("id") == "products":
              # Handle products lookup
              products = lookup_products(bearer_token=token,
                                         product_name=rule["value"])
              logger.info(f"Available products: {len(products)} items")

              if products:
                # Use LLM to select the most relevant product based on the query
                selected_product_id, product_input_tokens, product_output_tokens, product_total_tokens = select_relevant_product(
                  products, user_query)
                logger.info(
                  f"Selected product ID: {selected_product_id}, input_tokens={product_input_tokens}, output_tokens={product_output_tokens}, total_tokens={product_total_tokens}")

                # Add to the running total
                input_token += product_input_tokens
                output_token += product_output_tokens
                total_token_consumed += product_total_tokens

                if selected_product_id:
                  logger.info(f"Selected product ID: {selected_product_id}")
                  rule["value"] = selected_product_id
                else:
                  # Fallback to first product if LLM selection fails
                  rule["value"] = products[0]["id"]
                  logger.info(
                    f"Using first product as fallback for value: {rule['value']}")
              else:
                logger.info(f"No products found for query: {rule['value']}")
                continue
            elif rule.get("id") == "company":
              # Handle company lookup
              companies = lookup_companies(bearer_token=token,
                                           company_name=rule["value"])
              logger.info(f"Available companies: {len(companies)} items")

              if companies:
                # Use LLM to select the most relevant company based on the query
                selected_company_id, company_input_tokens, company_output_tokens, company_total_tokens = select_relevant_company(
                  companies, user_query)
                logger.info(
                  f"Selected company ID: {selected_company_id}, input_tokens={company_input_tokens}, output_tokens={company_output_tokens}, total_tokens={company_total_tokens}")

                # Add to the running total
                input_token += company_input_tokens
                output_token += company_output_tokens
                total_token_consumed += company_total_tokens

                if selected_company_id:
                  logger.info(f"Selected company ID: {selected_company_id}")
                  rule["value"] = selected_company_id
                else:
                  # Fallback to first company if LLM selection fails
                  rule["value"] = companies[0]["id"]
                  logger.info(
                    f"Using first company as fallback for value: {rule['value']}")
              else:
                logger.info(f"No companies found for query: {rule['value']}")
                continue
            elif rule.get("id") == "associatedContacts":
              # Handle contacts lookup
              contacts = lookup_contacts(bearer_token=token,
                                         first_name=rule["value"])
              logger.info(f"Available contacts: {len(contacts)} items")

              if contacts:
                # Use LLM to select the most relevant contact based on the query
                selected_contact_id, contact_input_tokens, contact_output_tokens, contact_total_tokens = select_relevant_contact(
                  contacts, user_query)
                logger.info(
                  f"Selected contact ID: {selected_contact_id}, input_tokens={contact_input_tokens}, output_tokens={contact_output_tokens}, total_tokens={contact_total_tokens}")

                # Add to the running total
                input_token += contact_input_tokens
                output_token += contact_output_tokens
                total_token_consumed += contact_total_tokens

                if selected_contact_id:
                  logger.info(f"Selected contact ID: {selected_contact_id}")
                  rule["value"] = selected_contact_id
                else:
                  # Fallback to first contact if LLM selection fails
                  rule["value"] = contacts[0]["id"]
                  logger.info(
                    f"Using first contact as fallback for value: {rule['value']}")
              else:
                logger.info(f"No contacts found for query: {rule['value']}")
                continue
            else:
              lookup_result = lookup_user(bearer_token=token,
                                          url=rule.get("lookupUrl", ""),
                                          first_name=rule["value"])
              logger.info(f"User lookup result - {lookup_result}")
              if lookup_result and "content" in lookup_result and lookup_result[
                "content"]:
                if len(lookup_result["content"]) > 0:
                  # Use LLM to select the most relevant user based on the query
                  selected_user_id, user_input_tokens, user_output_tokens, user_total_tokens = select_relevant_user(
                      lookup_result["content"], user_query)
                  logger.info(
                    f"Selected user ID: {selected_user_id}, input_tokens={user_input_tokens}, output_tokens={user_output_tokens}, total_tokens={user_total_tokens}")

                  # Add to the running total
                  input_token += user_input_tokens
                  output_token += user_output_tokens
                  total_token_consumed += user_total_tokens

                  if selected_user_id:
                    logger.info(f"Selected user ID: {selected_user_id}")
                    rule["value"] = selected_user_id
                  else:
                    # Fallback to first user if LLM selection fails
                    rule["value"] = lookup_result["content"][0]["id"]
                    logger.info(
                      f"Using first user as fallback for value: {rule['value']}")
                else:
                  logger.info(
                    f"Empty content list from lookup_user for value: {rule['value']}")
                  continue
              else:
                logger.info(
                  f"Invalid response from lookup_user for value: {rule['value']}")
                continue
          except (IndexError, KeyError) as e:
            logger.info(
              f"Error accessing lookup data: {str(e)} for value: {rule['value']}")
            continue

        elif isinstance(rule, dict) and rule.get("type") == "date":
          try:
            logger.info(
              f"Processing date field: {rule.get('id')} with value: {rule.get('value')}")
            logger.info(f"Fetching timezone for date field: {rule.get('id')}")
            get_timezone = get_time_zone(bearer_token=token)

            if get_timezone:
              rule["timeZone"] = get_timezone
              logger.info(
                f"Successfully set timezone '{get_timezone}' for date field: {rule.get('id')}")

              # Get current date in user's timezone for reference
              current_date = datetime.now(timezone.utc).strftime(
                "%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

              logger.info(f"Current date in user's timezone: {current_date}")

              # Ask LLM to select correct operator and format date value
              messages = [
                {
                  "role": "system",
                  "content": (
                    f"You are an assistant that selects the appropriate date operator and formats date values for a query in json. "
                    f"The user's timezone is {get_timezone}. Today's date is {current_date}.\n\n"
                    f"Available operators: greater, greater_or_equal, less, less_or_equal, between, not_between, "
                    f"is_not_null, is_null, today, yesterday, tomorrow, last_seven_days, next_seven_days, "
                    f"last_fifteen_days, next_fifteen_days, last_thirty_days, next_thirty_days, week_to_date, "
                    f"current_week, last_week, next_week, month_to_date, current_month, last_month, next_month, "
                    f"quarter_to_date, current_quarter, last_quarter, next_quarter, year_to_date, current_year, "
                    f"last_year, next_year, before_current_date_and_time, after_current_date_and_time.\n\n"
                    f"IMPORTANT: For phrases like 'before today', use the operator 'less' with today's date in UTC.\n"
                    f"For 'between' or 'not_between' operators, return an array of two ISO dates in UTC. "
                    f"For operators like 'greater', 'less', etc., return a single ISO date in UTC. "
                    f"For relative operators like 'today', 'yesterday', etc., use those exact operators with no value.\n\n"
                    f"IMPORTANT: When user mentions time relative to today or now (like 'X days from now', 'X months from today'):\n"
                    f"1. For short periods (within 7, 15, or 30 days), use the predefined operators when possible.\n"
                    f"2. For other periods, calculate the exact date by adding/subtracting from {current_date}.\n"
                    f"3. Always return dates in ISO format with time and timezone: YYYY-MM-DDTHH:MM:SS.sssZ\n"
                    f"4. For 'from today' or 'from now', calculate the exact future date based on current date ({current_date}).\n"
                    f"5. For 'X days ago' or 'X months ago', calculate the exact past date based on current date.\n\n"
                    f"Always prefer using the predefined operators (today, yesterday, etc.) when they match the user's intent."
                  )
                },
                {
                  "role": "user",
                  "content": f"Field: {rule.get('id')}, User query: {user_query}, Current value: {rule.get('value')}"
                }
              ]

              response = client.chat.completions.create(
                  messages=messages,
                  model="gpt-4o-mini",
                  response_format={"type": "json_object"}
              )

              # Add token tracking
              date_input_tokens = response.usage.prompt_tokens
              date_output_tokens = response.usage.completion_tokens
              date_total_tokens = response.usage.total_tokens

              # Add to the running total
              input_token += date_input_tokens
              output_token += date_output_tokens
              total_token_consumed += date_total_tokens

              logger.info(
                f"Date field LLM token usage - input={date_input_tokens}, output={date_output_tokens}, total={date_total_tokens}")
              date_info = json.loads(response.choices[0].message.content)

              if "operator" in date_info:
                rule["operator"] = date_info["operator"]

                # Handle different operator types
                if rule["operator"] in ["between", "not_between"]:
                  if "value" in date_info and isinstance(date_info["value"],
                                                         list) and len(
                      date_info["value"]) == 2:
                    rule["value"] = date_info["value"]
                  else:
                    logger.info(
                      f"Invalid value format for {rule['operator']} operator: {date_info.get('value')}")
                elif rule["operator"] in ["is_null", "is_not_null", "today",
                                          "yesterday", "tomorrow",
                                          "last_seven_days", "next_seven_days",
                                          "last_fifteen_days",
                                          "next_fifteen_days",
                                          "last_thirty_days",
                                          "next_thirty_days",
                                          "week_to_date", "current_week",
                                          "last_week", "next_week",
                                          "month_to_date", "current_month",
                                          "last_month", "next_month",
                                          "quarter_to_date", "current_quarter",
                                          "last_quarter", "next_quarter",
                                          "year_to_date", "current_year",
                                          "last_year", "next_year",
                                          "before_current_date_and_time",
                                          "after_current_date_and_time"]:
                  # These operators don't need a value
                  rule["value"] = None
                else:
                  # Single date operators
                  if "value" in date_info:
                    rule["value"] = date_info["value"]
                  else:
                    logger.warning(
                      f"Missing value for {rule['operator']} operator")

                logger.info(
                  f"Updated date field with operator: {rule['operator']} and value: {rule['value']}")
              else:
                logger.info(
                  f"Missing operator in LLM response for date field: {date_info}")
            else:
              logger.info(
                f"Empty or null response from get_time_zone for date field: {rule.get('id')}")
              continue
          except Exception as e:
            logger.error(
              f"Error processing date field {rule.get('id')}: {str(e)}")
            continue

        elif isinstance(rule, dict) and rule.get("id") == "pipeline":
          try:
            pipelines = get_pipeline(bearer_token=token,
                                     entity_type=entity_type)
            logger.info(f"Available pipelines: {pipelines}")
            if pipelines:
              # Select the most relevant pipeline based on user query
              pipeline_id, pipeline_input_tokens, pipeline_output_tokens, pipeline_total_tokens = select_relevant_pipeline(
                pipelines, user_query)
              logger.info(
                f"Selected pipeline ID: {pipeline_id}, input_tokens={pipeline_input_tokens}, output_tokens={pipeline_output_tokens}, total_tokens={pipeline_total_tokens}")
              # Add to the running total
              input_token += pipeline_input_tokens
              output_token += pipeline_output_tokens
              total_token_consumed += pipeline_total_tokens

              logger.info(f"Selected pipeline id: {pipeline_id}")

              if pipeline_id:
                logger.info(f"Setting pipeline value to: {pipeline_id}")
                rule["value"] = pipeline_id

              else:
                logger.info("No pipeline selected from available pipelines")
                continue
            else:
              logger.info("No pipelines returned from lookup_pipeline")
              continue
          except (IndexError, KeyError, ValueError) as e:
            logger.info(f"Error processing pipeline data: {str(e)}")
            continue

        elif isinstance(rule, dict) and rule.get("id") == "pipelineStage":
          try:
            pipelines = get_pipeline(bearer_token=token, entity_type=entity_type)
            logger.info(f"Available pipelines: {pipelines}")
            if pipelines:
              # Select the most relevant pipeline based on user query
              pipeline_id, pipeline_input_tokens, pipeline_output_tokens, pipeline_total_tokens = select_relevant_pipeline(
                pipelines, user_query)

              # Add to the running total
              input_token += pipeline_input_tokens
              output_token += pipeline_output_tokens
              total_token_consumed += pipeline_total_tokens

              logger.info(f"Selected pipeline id: {pipeline_id}")

              if pipeline_id:
                # Add pipeline rule
                generated_filter["jsonRule"]["rules"].append({
                  "operator": "equal",
                  "id": "pipeline",
                  "field": "pipeline",
                  "type": "long",
                  "value": pipeline_id,
                  "dependentFieldIds": ["pipelineStage", "pipelineStageReason"]
                })

                # Get stages for the selected pipeline
                pipeline_stages = get_pipeline_stages(bearer_token=token,
                                                      pipeline_id=pipeline_id)
                logger.info(f"Pipeline stages: {pipeline_stages}")

                if pipeline_stages:
                  selected_valid_stage, stage_input_tokens, stage_output_tokens, stage_total_tokens = select_valid_pipeline_stage(
                    stages=pipeline_stages, user_query=user_query)

                  # Add to the running total
                  input_token += stage_input_tokens
                  output_token += stage_output_tokens
                  total_token_consumed += stage_total_tokens

                  logger.info(
                    f"Selected valid stage from pipeline - {selected_valid_stage}")

                  if selected_valid_stage is not None and str(
                      selected_valid_stage).strip():
                    try:
                      rule["value"] = int(selected_valid_stage)
                    except ValueError:
                      logger.info(
                        f"Could not convert pipeline stage '{selected_valid_stage}' to int")
                      # Fall back to using all stages
                      rule["operator"] = "in"
                      rule["value"] = [stage["id"] for stage in pipeline_stages
                                       if "id" in stage]
                      logger.info(f"Using all pipeline stages: {rule['value']}")
                  else:
                    # No valid stage selected, use all stages
                    logger.info(
                      "No valid pipeline stage selected, using all stages instead")
                    rule["operator"] = "in"
                    rule["value"] = [stage["id"] for stage in pipeline_stages if
                                     "id" in stage]
                    logger.info(f"Using all pipeline stages: {rule['value']}")
                else:
                  logger.info(
                    f"No pipeline stages found for pipeline_id: {pipeline_id}")
                  continue
              else:
                logger.info("No pipeline selected from available pipelines")
                continue
            else:
              logger.info("No pipelines returned from lookup_pipeline")
              continue
          except (IndexError, KeyError) as e:
            logger.info(f"Error accessing pipeline data: {str(e)}")
            continue

    inserted_data = request_history_service.insert_request(
        user_id=user_id,
        tenant_id=tenant_id,
        requested_input=request.model_dump_json(),
        generated_response=generated_filter,
        request_date=datetime.now(timezone.utc),
        email_id=None,
        input_token_consumed=input_token,
        output_token_consumed=output_token,
        total_consumed=total_token_consumed,
        response_accepted_status=True,
        entity_id=None,
        entity_type=generated_filter["entityType"],
        operation="search",
    )

    logger.info(
        f"Generated filters - tenantId={tenant_id}, userId={user_id}, generatedFilters={generated_filter}"
    )
    return generated_filter

  except RateLimitExceededException as openai_rate_err:
    logger.error(
        f"OpenAI API rate limit exceeded: {str(openai_rate_err)}",
        extra={"user_query": user_query, "tenant_id": tenant_id}
    )
    raise RateLimitExceededException(
        message="OpenAI API rate limit exceeded. Please try again later.")


  except ValueError as val_err:
    logger.error(
        f"Value error in generate_filters: {str(val_err)}",
        extra={"user_query": user_query, "tenant_id": tenant_id}
    )
    raise HTTPException(
        status_code=400,
        detail=f"Error processing filters due to value error: generate_filters {str(val_err)}",
    )
  except Exception as e:
    logger.error(
        f"Error in generate_filters: {str(e)}",
        extra={"user_query": user_query, "tenant_id": tenant_id}
    )
    raise HTTPException(
        status_code=400,
        detail=f"Error processing filters: generate_filters {str(e)}",
    )

def run_multi_vector_search(conditions: list[str], tenant_id: int,
      entity: str) -> list[dict]:
    all_results = []

    for condition in conditions:
      logger.info(f"User query before - {condition}")
      condition = clean_user_query(condition)
      logger.info(f"Cleaned user query - {condition}")

      logger.info(f"Searching for condition: {condition}")
      vector = get_embedding(condition)
      results = search_similar_documents_in_standard_picklist(vector, tenant_id,
                                                              entity)
      all_results.append(results)

    return all_results