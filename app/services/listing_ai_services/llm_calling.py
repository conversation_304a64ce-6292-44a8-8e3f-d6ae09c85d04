import json
from typing import Any, List, Dict

from fastapi import H<PERSON><PERSON><PERSON>xception
from openai import OpenAI, RateLimitError
from datetime import datetime, timezone
import os
from dotenv import load_dotenv

from app.exceptions import RateLimitExceededException
from app.services.logger_config import logger

from app.services.listing_ai_services.listing_pydantic_models import (
    ListingAiResponseFormat,
)

load_dotenv()

openai_api_key = os.getenv("OPENAI_API_KEY")

client = OpenAI(api_key=openai_api_key)

ENTITY_FIELDS = [
    "ownerFields",
    "createdByFields",
    "updatedByFields",
    "importedByFields",
    "convertedByFields",
]

LOOK_UP_FIELDS = [
    "products",
    "ownerId",
    "convertedBy",
    "createdBy",
    "updatedBy",
    "importedBy",
]


def generate_llm_filters(data: List[Dict[str, Any]], user_query: str, entity_type: str):
    logger.info(
        f"Generating llm filters - data={data} user_query={user_query}, entity_type={entity_type}"
    )
    try:

        # Get current date for reference in ISO 8601 format with milliseconds and Z timezone
        current_date = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        logger.info(f"Current date reference: {current_date}")

        # Add current date to the prompt
        date_context = f"""
        <date_reference>
        * Current date and time (UTC): {current_date}
        * Use this as reference for any relative date calculations
        </date_reference>
        """

        messages = [
            {
                "role": "system",
                "content": (
                    f"""
                    <identity>
                    You are an AI that converts natural language queries into structured JSON filters.
                    </identity>

                    <instructions>
                    * You are generating filters for entity type: {entity_type}
                    * Use ONLY the provided filterable fields
                    * Use correct types (numeric for "double"/"long", not strings)
                    * Populate "value" field based on user input and assign it to "value" field and ignore type while choosing value
                    * Don't alter fields except "value"
                    * Choose appropriate operators from available options
                    * Return empty jsonRule if fields insufficient
                    * Use "AND" condition based
                    * For owner-related queries:
                      - If entity type is "deal" or "company", use "ownedBy" as id and field, with type "long"
                      - Otherwise, use "ownerId" as id and field, with type "long"
                    * If operator is "is_null", "is_not_null", "is_empty", or "is_not_empty", set value to null
                
                    </instructions>

                    {date_context}
                    
                    <entity_type>
                      {entity_type}
                    </entity_type>

                    <filterable_fields>
                    {data}
                    </filterable_fields>

                    <examples>
                    <example1>
                    <user_query>Give me leads whose first name is Sumit and last name is Suryawanshi and Expected Closure On is onwards 1st March 2025 and business type is analyst</user_query>
                    <response>
                    {{
                      "jsonRule": {{
                        "rules": [
                          {{
                            "operator": "equal",
                            "id": "firstName",
                            "field": "firstName",
                            "type": "string",
                            "value": "Sumit",
                            "relatedFieldIds": null
                          }},
                          {{
                            "operator": "equal",
                            "id": "lastName",
                            "field": "lastName",
                            "type": "string",
                            "value": "Suryawanshi",
                            "relatedFieldIds": null
                          }},
                           {{
                            "operator": "equal",
                            "id": "companyBusinessType",
                            "field": "companyBusinessType",
                            "type": "string",
                            "value": "analyst",
                            "relatedFieldIds": null
                          }},
                          {{
                            "operator": "greater_or_equal",
                            "id": "expectedClosureOn",
                            "field": "expectedClosureOn",
                            "type": "date",
                            "value": "2025-03-01T06:00:00.000Z",
                            "relatedFieldIds": null,
                            "timeZone": "Asia/Calcutta"
                          }}
                        ],
                        "condition": "AND",
                        "valid": true
                      }},
                      "entityType": "lead"
                    }}
                    </response>
                    </example1>

                    <example2>
                    <user_query>Show me Soldierz Owner John Smith</user_query>
                    <response>
                    {{
                      "jsonRule": {{
                        "rules": [
                          {{
                            "operator": "equal",
                            "id": "ownerId",
                            "field": "ownerId",
                            "type": "long",
                            "value": "John Smith",
                            "relatedFieldIds": null
                          }}
                        ],
                        "condition": "AND",
                        "valid": true
                      }},
                      "entityType": "lead"
                    }}
                    </response>
                    </example2>

                    <example3>
                    <user_query>give me Deals whose source is cold calling</user_query>
                    <response>
                    {{
                      "jsonRule": {{
                        "rules": [
                            {{
                                "operator": "equal",
                                "id": "source",
                                "field": "source",
                                "type": "long",
                                "value": 4267,
                                "relatedFieldIds": null
                            }}
                        ],
                        "condition": "AND",
                        "valid": true
                      }},
                      "entityType": "deal"
                    }}
                    </response>
                    </example3>
                    
                    <example4>
                    <user_query>Show me companies Owner John Smith and business type is analyst</user_query>
                    <response>
                    {{
                      "jsonRule": {{
                        "rules": [
                          {{
                            "operator": "equal",
                            "id": "ownedBy",
                            "field": "ownedBy",
                            "type": "long",
                            "value": "John Smith",
                            "relatedFieldIds": null
                          }},
                           {{
                            "operator": "equal",
                            "id": "businessType",
                            "field": "businessType",
                            "type": "long",
                            "value": 1234,
                            "relatedFieldIds": null
                          }}
                        ],
                        "condition": "AND",
                        "valid": true
                      }},
                      "entityType": "company"
                    }}
                    </response>
                    </example4>
                    </examples>
                    """
                ),
            },
            {"role": "user", "content": f"user input query: {user_query}"},
        ]

        chat_completion = client.beta.chat.completions.parse(
            messages=messages,
            model="gpt-4o-mini",
            response_format=ListingAiResponseFormat,
        )

        input_token = chat_completion.usage.prompt_tokens
        output_token = chat_completion.usage.completion_tokens
        total_token_consumed = chat_completion.usage.total_tokens

        return (
            input_token,
            output_token,
            total_token_consumed,
            chat_completion.choices[0].message.parsed,
        )

    except RateLimitError as openai_rate_err:
        logger.error(
            "OpenAI API rate limit exceeded",
            extra={
                "error": str(openai_rate_err),
                "user_query": user_query,
            },
        )
        raise RateLimitExceededException(
            "OpenAI API rate limit exceeded. Please try again later."
        )

    except Exception as e:
        logger.error(
            "Error in llm_calling",
            extra={
                "error": str(e),
                "user_query": user_query,
                "data": data,
            },
        )
        raise HTTPException(
            status_code=400,
            detail="Error processing filters: llm_calling " + str(e),
        )


def generate_llm_filters_company(data: List[Dict[str, Any]], user_query: str,
    entity_type: str):
    logger.info(
        f"Generating llm filters company - data={data} user_query={user_query}, entity_type={entity_type}"
    )
    try:

        # Get current date for reference in ISO 8601 format with milliseconds and Z timezone
        current_date = datetime.now(timezone.utc).strftime(
            "%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        logger.info(f"Current date reference: {current_date}")

        # Add current date to the prompt
        date_context = f"""
        <date_reference>
        * Current date and time (UTC): {current_date}
        * Use this as reference for any relative date calculations
        </date_reference>
        """

        messages = [
            {
                "role": "system",
                "content": (
                    f"""
                    <identity>
                    You are an AI that converts natural language queries into structured JSON filters.
                    </identity>

                    <instructions>
                    * You are generating filters for entity type: {entity_type}
                    * Use ONLY the provided filterable fields
                    * Populate "valueId" field based on user input and assign it to "value" field
                    * Use correct types (numeric for "double"/"long", not strings)
                    * Don't alter fields except "value"
                    * Choose appropriate operators from available options
                    * Return empty jsonRule if fields insufficient
                    * Use "AND" condition based
                    * For owner-related queries:
                      - If entity type is "deal" or "company", use "ownedBy" as id and field, with type "long"
                      - Otherwise, use "ownerId" as id and field, with type "long"
                    * If operator is "is_null", "is_not_null", "is_empty", or "is_not_empty", set value to null

                    </instructions>

                    {date_context}

                    <entity_type>
                      {entity_type}
                    </entity_type>

                    <filterable_fields>
                    {data}
                    </filterable_fields>

                    <examples>
                    <example1>
                    <user_query>Give me leads whose first name is Sumit and last name is Suryawanshi and Expected Closure On is onwards 1st March 2025 and business type is analyst</user_query>
                    <response>
                    {{
                      "jsonRule": {{
                        "rules": [
                          {{
                            "operator": "equal",
                            "id": "firstName",
                            "field": "firstName",
                            "type": "string",
                            "value": "Sumit",
                            "relatedFieldIds": null
                          }},
                          {{
                            "operator": "equal",
                            "id": "lastName",
                            "field": "lastName",
                            "type": "string",
                            "value": "Suryawanshi",
                            "relatedFieldIds": null
                          }},
                           {{
                            "operator": "equal",
                            "id": "companyBusinessType",
                            "field": "companyBusinessType",
                            "type": "string",
                            "value": "analyst",
                            "relatedFieldIds": null
                          }},
                          {{
                            "operator": "greater_or_equal",
                            "id": "expectedClosureOn",
                            "field": "expectedClosureOn",
                            "type": "date",
                            "value": "2025-03-01T06:00:00.000Z",
                            "relatedFieldIds": null,
                            "timeZone": "Asia/Calcutta"
                          }}
                        ],
                        "condition": "AND",
                        "valid": true
                      }},
                      "entityType": "lead"
                    }}
                    </response>
                    </example1>

                    <example2>
                    <user_query>Show me Soldierz Owner John Smith</user_query>
                    <response>
                    {{
                      "jsonRule": {{
                        "rules": [
                          {{
                            "operator": "equal",
                            "id": "ownerId",
                            "field": "ownerId",
                            "type": "long",
                            "value": "John Smith",
                            "relatedFieldIds": null
                          }}
                        ],
                        "condition": "AND",
                        "valid": true
                      }},
                      "entityType": "lead"
                    }}
                    </response>
                    </example2>

                    <example3>
                    <user_query>give me Deals whose source is cold calling</user_query>
                    <response>
                    {{
                      "jsonRule": {{
                        "rules": [
                            {{
                                "operator": "equal",
                                "id": "source",
                                "field": "source",
                                "type": "long",
                                "value": 4267,
                                "relatedFieldIds": null
                            }}
                        ],
                        "condition": "AND",
                        "valid": true
                      }},
                      "entityType": "deal"
                    }}
                    </response>
                    </example3>

                    <example4>
                    <user_query>Show me companies Owner John Smith and business type is analyst</user_query>
                    <response>
                    {{
                      "jsonRule": {{
                        "rules": [
                          {{
                            "operator": "equal",
                            "id": "ownedBy",
                            "field": "ownedBy",
                            "type": "long",
                            "value": "John Smith",
                            "relatedFieldIds": null
                          }},
                           {{
                            "operator": "equal",
                            "id": "businessType",
                            "field": "businessType",
                            "type": "long",
                            "value": 1234,
                            "relatedFieldIds": null
                          }}
                        ],
                        "condition": "AND",
                        "valid": true
                      }},
                      "entityType": "company"
                    }}
                    </response>
                    </example4>
                    </examples>
                    """
                ),
            },
            {"role": "user", "content": f"user input query: {user_query}"},
        ]

        chat_completion = client.beta.chat.completions.parse(
            messages=messages,
            model="gpt-4o-mini",
            response_format=ListingAiResponseFormat,
        )

        input_token = chat_completion.usage.prompt_tokens
        output_token = chat_completion.usage.completion_tokens
        total_token_consumed = chat_completion.usage.total_tokens

        return (
            input_token,
            output_token,
            total_token_consumed,
            chat_completion.choices[0].message.parsed,
        )

    except RateLimitError as openai_rate_err:
        logger.error(
            "OpenAI API rate limit exceeded",
            extra={
                "error": str(openai_rate_err),
                "user_query": user_query,
            },
        )
        raise RateLimitExceededException(
            "OpenAI API rate limit exceeded. Please try again later."
        )

    except Exception as e:
        logger.error(
            "Error in llm_calling",
            extra={
                "error": str(e),
                "user_query": user_query,
                "data": data,
            },
        )
        raise HTTPException(
            status_code=400,
            detail="Error processing filters: llm_calling " + str(e),
        )


def select_valid_pipeline_stage(stages: List[Dict[str, Any]], user_query: str):
    logger.info(f"Selecting valid stage from pipeline - user_query={user_query} stages={stages}")

    try:
        messages = [
            {
                "role": "system",
                "content": (
                    """
                    <identity>
                    You are an assistant that selects the most appropriate pipeline stage ID based on user query and available stages.
                    </identity>

                    <instructions>
                    * Your task is to analyze the user's intent and return the "id" of the most relevant stage.
                    * Match the user query with the "name" and "description" fields of the stages.
                    * If no stage matches confidently, return null.
                    * Do not generate or alter any stage data. Only select from the provided list.
                    * Always respond with only the integer ID or null, nothing else.
                    </instructions>

                    <reasoning_steps>
                    1. Understand the user's intent and stage context.
                    2. Find the best matching stage from the provided options.
                    3. Return only the ID of the matched stage.
                    </reasoning_steps>

                    <stages>
                    %s
                    </stages>
                    """ % json.dumps(stages, indent=2)
                )
            },
            {
                "role": "user",
                "content": f"User input query: {user_query}"
            }
        ]

        response = client.chat.completions.create(
            messages=messages,
            model="gpt-4o-mini",
            response_format={"type": "text"}
        )

        # Add token tracking
        input_tokens = response.usage.prompt_tokens
        output_tokens = response.usage.completion_tokens
        total_tokens = response.usage.total_tokens

        logger.info(f"Pipeline stage LLM token usage - input={input_tokens}, output={output_tokens}, total={total_tokens}")
        logger.info(f"LLM response: {response}")

        # Return tokens along with the result
        content = response.choices[0].message.content.strip()
        result = int(content) if content.lower() != "null" else None
        return result, input_tokens, output_tokens, total_tokens

    except Exception as e:
        logger.exception("Error selecting pipeline stage")  # Logs traceback
        raise HTTPException(
            status_code=400,
            detail=f"Error selecting pipeline stage: {str(e)}"
        )


def select_relevant_user(users: List[Dict[str, Any]], user_query: str):
    logger.info(f"Selecting relevant user - user_query={user_query} users={users}")

    try:
        if not users or len(users) == 0:
            logger.info("No users provided to select from")
            return None, 0, 0, 0

        # If only one user, return it directly
        if len(users) == 1:
            return users[0]["id"], 0, 0, 0

        messages = [
            {
                "role": "system",
                "content": (
                    """
                    <identity>
                    You are an assistant that selects the most relevant user based on a query.
                    </identity>

                    <instructions>
                    * Your task is to analyze the user's query and return the ID of the most relevant user.
                    * Match the user query with the user information provided.
                    * If no user matches confidently, return the first user's ID.
                    * Always respond with only the user ID, nothing else.
                    </instructions>

                    <reasoning_steps>
                    1. Understand the user's query and what they're looking for.
                    2. Find the best matching user from the provided options.
                    3. Return only the ID of the matched user.
                    </reasoning_steps>

                    <users>
                    %s
                    </users>
                    """ % json.dumps(users, indent=2)
                )
            },
            {
                "role": "user",
                "content": f"User input query: {user_query}"
            }
        ]

        response = client.chat.completions.create(
            messages=messages,
            model="gpt-4o-mini",
            response_format={"type": "text"}
        )

        # Track token usage
        input_tokens = response.usage.prompt_tokens
        output_tokens = response.usage.completion_tokens
        total_tokens = response.usage.total_tokens

        logger.info(f"LLM token usage for user selection - input={input_tokens}, output={output_tokens}, total={total_tokens}")
        logger.info(f"LLM response for user selection: {response}")

        # Extract text safely
        content = response.choices[0].message.content.strip()
        logger.info(f"LLM response for content: {content}")

        # Convert all user IDs to strings for comparison
        user_ids_str = [str(user.get("id")) for user in users]
        logger.info(f"User IDs (as strings): {user_ids_str}")

        # Convert content to string for comparison
        content_str = str(content)

        # Check if content matches any ID as string
        if content_str in user_ids_str:
            logger.info(f"Selected user ID '{content}' is valid")
            # Find the original user with this ID
            for user in users:
                if str(user.get("id")) == content_str:
                    return user.get("id"), input_tokens, output_tokens, total_tokens
            # Fallback if somehow we didn't find it (shouldn't happen)
            return users[0]["id"], input_tokens, output_tokens, total_tokens
        else:
            # Default to first user if LLM response is not valid
            logger.info(f"LLM returned invalid user ID: '{content}'. Using first user instead.")
            return users[0]["id"], input_tokens, output_tokens, total_tokens

    except Exception as e:
        logger.exception("Error selecting relevant user")  # Logs traceback
        # Default to first user in case of error
        return users[0]["id"] if users else None, 0, 0, 0


def select_relevant_pipeline(pipelines: List[Dict[str, Any]], user_query: str):
    logger.info(f"Selecting relevant pipeline - user_query={user_query} pipelines={pipelines}")

    try:
        if not pipelines or len(pipelines) == 0:
            logger.info("No pipelines provided to select from")
            return None, 0, 0, 0

        # If only one pipeline, return it directly
        if len(pipelines) == 1:
            return pipelines[0]["id"], 0, 0, 0

        messages = [
            {
                "role": "system",
                "content": (
                    """
                    <identity>
                    You are an assistant that selects the most relevant pipeline based on a query.
                    </identity>

                    <instructions>
                    * Your task is to analyze the user's query and return the ID of the most relevant pipeline.
                    * Match the user query with the pipeline names provided.
                    * If no pipeline matches confidently, return the ID of the "Default Lead Pipeline".
                    * If there is no "Default Lead Pipeline", return the first pipeline's ID.
                    * Always respond with only the pipeline ID, nothing else.
                    </instructions>

                    <pipelines>
                    %s
                    </pipelines>
                    """ % json.dumps(pipelines, indent=2)
                )
            },
            {
                "role": "user",
                "content": f"User input query: {user_query}"
            }
        ]

        response = client.chat.completions.create(
            messages=messages,
            model="gpt-4o-mini",
            response_format={"type": "text"}
        )

        # Track token usage
        input_tokens = response.usage.prompt_tokens
        output_tokens = response.usage.completion_tokens
        total_tokens = response.usage.total_tokens

        logger.info(f"LLM token usage for pipeline selection - input={input_tokens}, output={output_tokens}, total={total_tokens}")
        logger.info(f"LLM response for pipeline selection: {response}")

        # Extract text safely
        content = response.choices[0].message.content.strip()

        # Try to convert to int
        try:
            return int(content), input_tokens, output_tokens, total_tokens
        except ValueError:
            # If conversion fails, look for default pipeline
            for pipeline in pipelines:
                if pipeline["name"].lower() == "default lead pipeline":
                    return pipeline["id"], input_tokens, output_tokens, total_tokens
            # Fallback to first pipeline
            return pipelines[0]["id"], input_tokens, output_tokens, total_tokens

    except Exception as e:
        logger.exception("Error selecting relevant pipeline")
        # Default to first pipeline in case of error
        return pipelines[0]["id"] if pipelines else None, 0, 0, 0


def select_relevant_product(products: List[Dict[str, Any]], user_query: str):
    logger.info(f"Selecting relevant product - user_query={user_query}")

    try:
        if not products or len(products) == 0:
            logger.info("No products provided to select from")
            return None, 0, 0, 0

        # If only one product, return it directly
        if len(products) == 1:
            return products[0]["id"], 0, 0, 0

        messages = [
            {
                "role": "system",
                "content": (
                    """
                    <identity>
                    You are an assistant that selects the most relevant product based on a user query.
                    </identity>

                    <instructions>
                    * Analyze the user's query and return the ID of the most relevant product.
                    * Match keywords in the query with product names, descriptions, and other attributes.
                    * If no product matches confidently, return the ID of the first product.
                    * Always respond with only the product ID, nothing else.
                    </instructions>

                    <products>
                    %s
                    </products>
                    """ % json.dumps(products, indent=2)
                )
            },
            {
                "role": "user",
                "content": f"User input query: {user_query}"
            }
        ]

        response = client.chat.completions.create(
            messages=messages,
            model="gpt-4o-mini",
            response_format={"type": "text"}
        )

        # Track token usage
        input_tokens = response.usage.prompt_tokens
        output_tokens = response.usage.completion_tokens
        total_tokens = response.usage.total_tokens

        logger.info(f"LLM token usage for product selection - input={input_tokens}, output={output_tokens}, total={total_tokens}")
        logger.info(f"LLM response for product selection: {response}")

        # Extract text safely
        content = response.choices[0].message.content.strip()

        # Try to convert to int
        try:
            return int(content), input_tokens, output_tokens, total_tokens
        except ValueError:
            # Fallback to first product
            return products[0]["id"], input_tokens, output_tokens, total_tokens

    except Exception as e:
        logger.exception("Error selecting relevant product")
        # Default to first product in case of error
        return products[0]["id"] if products else None, 0, 0, 0


def select_relevant_company(companies: List[Dict[str, Any]], user_query: str):
    logger.info(f"Selecting relevant company - user_query={user_query}")

    try:
        if not companies or len(companies) == 0:
            logger.info("No companies provided to select from")
            return None, 0, 0, 0

        # If only one company, return it directly
        if len(companies) == 1:
            return companies[0]["id"], 0, 0, 0

        messages = [
            {
                "role": "system",
                "content": (
                    """
                    <identity>
                    You are an assistant that selects the most relevant company based on a user query.
                    </identity>

                    <instructions>
                    * Analyze the user's query and return the ID of the most relevant company.
                    * Match keywords in the query with company names, descriptions, and other attributes.
                    * If no company matches confidently, return the ID of the first company.
                    * Always respond with only the company ID, nothing else.
                    </instructions>

                    <reasoning_steps>
                    1. Understand the user's query and what they're looking for.
                    2. Find the best matching company from the provided options.
                    3. Return only the ID of the matched company.
                    </reasoning_steps>

                    <companies>
                    %s
                    </companies>
                    """ % json.dumps(companies, indent=2)
                )
            },
            {
                "role": "user",
                "content": f"User input query: {user_query}"
            }
        ]

        response = client.chat.completions.create(
            messages=messages,
            model="gpt-4o-mini",
            response_format={"type": "text"}
        )

        # Track token usage
        input_tokens = response.usage.prompt_tokens
        output_tokens = response.usage.completion_tokens
        total_tokens = response.usage.total_tokens

        logger.info(f"LLM token usage for company selection - input={input_tokens}, output={output_tokens}, total={total_tokens}")
        logger.info(f"LLM response for company selection: {response}")

        # Extract text safely
        content = response.choices[0].message.content.strip()

        # Try to convert to int
        try:
            return int(content), input_tokens, output_tokens, total_tokens
        except ValueError:
            # Fallback to first company
            return companies[0]["id"], input_tokens, output_tokens, total_tokens

    except Exception as e:
        logger.exception("Error selecting relevant company")
        # Default to first company in case of error
        return companies[0]["id"] if companies else None, 0, 0, 0


def select_relevant_contact(contacts: List[Dict[str, Any]], user_query: str):
    logger.info(f"Selecting relevant contact - user_query={user_query}")

    try:
        if not contacts or len(contacts) == 0:
            logger.info("No contacts provided to select from")
            return None, 0, 0, 0

        # If only one contact, return it directly
        if len(contacts) == 1:
            return contacts[0]["id"], 0, 0, 0

        messages = [
            {
                "role": "system",
                "content": (
                    """
                    <identity>
                    You are an assistant that selects the most relevant contact based on a user query.
                    </identity>

                    <instructions>
                    * Analyze the user's query and return the ID of the most relevant contact.
                    * Match keywords in the query with contact names, email addresses, and other attributes.
                    * If no contact matches confidently, return the ID of the first contact.
                    * Always respond with only the contact ID, nothing else.
                    </instructions>

                    <reasoning_steps>
                    1. Understand the user's query and what they're looking for.
                    2. Find the best matching contact from the provided options.
                    3. Return only the ID of the matched contact.
                    </reasoning_steps>

                    <contacts>
                    %s
                    </contacts>
                    """ % json.dumps(contacts, indent=2)
                )
            },
            {
                "role": "user",
                "content": f"User input query: {user_query}"
            }
        ]

        response = client.chat.completions.create(
            messages=messages,
            model="gpt-4o-mini",
            response_format={"type": "text"}
        )

        # Track token usage
        input_tokens = response.usage.prompt_tokens
        output_tokens = response.usage.completion_tokens
        total_tokens = response.usage.total_tokens

        logger.info(f"LLM token usage for contact selection - input={input_tokens}, output={output_tokens}, total={total_tokens}")
        logger.info(f"LLM response for contact selection: {response}")

        # Extract text safely
        content = response.choices[0].message.content.strip()

        # Try to convert to int
        try:
            return int(content), input_tokens, output_tokens, total_tokens
        except ValueError:
            # Fallback to first contact
            return contacts[0]["id"], input_tokens, output_tokens, total_tokens

    except Exception as e:
        logger.exception("Error selecting relevant contact")
        # Default to first contact in case of error
        return contacts[0]["id"] if contacts else None, 0, 0, 0