import json
import logging
import os
from http.client import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from typing import Optional

from openai import OpenAI, RateLimitError
from elasticsearch import Elasticsearch, exceptions
from elasticsearch.helpers import bulk
from elasticsearch import helpers

# Setup logging
from app.services.logger_config import logger

openai_api_key = "********************************************************************************************************************************************************************"
client = OpenAI(api_key=openai_api_key)

# Elasticsearch Setup
es = Elasticsearch("http://elasticsearch-vector-master:9200")


# Index mapping
mapping = {
  "mappings": {
    "dynamic": "strict",
    "properties": {
      "vector": {"type": "dense_vector", "dims": 1024, "index": True,
                 "similarity": "cosine"},
      "text": {"type": "text"},
      "entity_type": {"type": "keyword"},
      "filter_id": {"type": "keyword"},
      "filter_details": {"type": "text"},
      "tenantId": {"type": "integer"}
    }
  }
}

# Mappings and options
type_mapping = {
  "TEXT_FIELD": "string", "NUMBER": "double", "URL": "string",
  "CHECKBOX": "boolean",
  "EMAIL": "string", "PHONE": "string", "TOGGLE": "boolean",
  "FORECASTING_TYPE": "string",
  "PARAGRAPH_TEXT": "string",
}
operator_mapping = {
  "TEXT_FIELD": "equal / not_equal / contains / not_contains / in / not_in / is_empty / is_not_empty / begins_with",
  "PARAGRAPH_TEXT": "equal / not_equal / contains / not_contains / in / not_in / is_empty / is_not_empty / begins_with",
  "NUMBER": "equal / not_equal / greater / greater_or_equal / less / less_or_equal / between / not_between / in / not_in / is_null / is_not_null",
  "URL": "equal / not_equal / contains / not_contains / in / not_in / is_empty / is_not_empty / begins_with",
  "CHECKBOX": "equal / not_equal",
  "PICK_LIST": "equal / not_equal / is_not_null / is_null / in / not_in",
  "MULTI_PICKLIST": "equal / not_equal / is_not_null / is_null / in / not_in",
  "DATETIME_PICKER": "...",
  "EMAIL": "equal / not_equal / contains / not_contains / in / not_in / is_empty / is_not_empty / begins_with",
  "PHONE": "equal / not_equal / contains / not_contains / in / not_in / is_empty / is_not_empty / begins_with",
  "TOGGLE": "equal / not_equal",
  "FORECASTING_TYPE": "equal / not_equal / in / not_in / is_empty / is_not_empty",
  "ENTITY_FIELDS": "equal / not_equal / in / not_in / is_not_null / is_null",
  "LOOK_UP": "equal / not_equal / is_not_null / is_null / in / not_in",
  "PIPELINE_STAGE": "equal / not_equal / in / not_in",
  "PIPELINE": "equal / not_equal / is_not_null / is_null / in / not_in"
}
static_options = {
  "TOGGLE": "true / false",
  "FORECASTING_TYPE": "OPEN / CLOSED_WON / CLOSED_UNQUALIFIED / CLOSED_LOST",
}
standard_picklist = {"requirementCurrency", "country", "timezone",
                     "companyCountry" }

standard_picklist_industry_business_Lead = {"companyIndustry", "companyBusinessType"}
standard_picklist_industry_business_company = {"industry", "businessType"}


def get_embedding(text: str) -> list[float]:
  try:
    logger.info(
        f"Generating embedding for text: {text[:50]}...")
    response = client.embeddings.create(
        input=text,
        model="text-embedding-3-small"
    )
    logger.info(f"Embedding generated successfully for text: {text[:50]}...")
    return response.data[0].embedding[:1024]
  except Exception as e:
    logger.error(f"Embedding error: {e}")
    return []


def create_index_for_standard_picklist(base_index_name: str) -> None:
  """
  Creates a new index and alias in Elasticsearch for the given tenant and entity type.
  If an index with the same base name exists, it checks if it has less than 2 aliases.
  If so, it uses that index; otherwise, it creates a new one.
  """

  logger.info(f"Creating index 'standard_picklist'...")
  if not es.indices.exists(index=base_index_name):
    logger.info(f"Index {base_index_name} does not exist, creating it...")
    es.indices.create(index=base_index_name, mappings=mapping["mappings"])
    logger.info(f"Created new index: {base_index_name}")


def create_index_and_alias(base_index_name: str, tenant_id: int,
    entityType: str) -> str:
  alias_name = f"{tenant_id}-{entityType}"

  def alias_count(index):
    try:
      result = es.indices.get_alias(index=index)
      return len(list(result[index]["aliases"].keys()))
    except Exception:
      return 0

  i = 1
  while True:
    index_name = f"{base_index_name.rsplit('-', 1)[0]}-{i}"
    if not es.indices.exists(index=index_name):
      es.indices.create(index=index_name, mappings=mapping["mappings"])
      logger.info(f"Created new index: {index_name}")
      break
    elif alias_count(index_name) < 200:
      logger.info(f"Using existing index: {index_name} (aliases count < 200)")
      break
    else:
      logger.info(f"Index {index_name} has 200 aliases, checking next...")
    i += 1

  try:
    if es.indices.exists_alias(name=alias_name):
      # Get all indices currently using the alias
      alias_info = es.indices.get_alias(name=alias_name)
      existing_indices = list(alias_info.keys())

      # Remove alias from all existing indices
      actions = [{"remove": {"index": idx, "alias": alias_name}} for idx in
                 existing_indices]

      # Add new alias to the selected index with filter
      actions.append({
        "add": {
          "index": index_name,
          "alias": alias_name,
          "is_write_index": True,
          "filter": {"term": {"tenantId": tenant_id}}
        }
      })

      es.indices.update_aliases(actions=actions)
      logger.info(
          f"Alias '{alias_name}' updated to point to index: {index_name}")

    else:
      # Alias doesn't exist; just add it
      es.indices.update_aliases(
          actions=[
            {
              "add": {
                "index": index_name,
                "alias": alias_name,
                "is_write_index": True,
                "filter": {"term": {"tenantId": tenant_id}}
              }
            }
          ]
      )
      logger.info(f"Alias '{alias_name}' created for index: {index_name}")

  except exceptions.ApiError as e:
    logger.warning(f"Alias update failed for tenant {tenant_id}: {e}")

  return alias_name


def search_similar_documents(vector: list[float], tenant_id: int, entity_type:str, k: int = 5):
  if len(vector) > 1024:
    vector = vector[:1024]

  alias_name = f"{tenant_id}-"+entity_type

  logger.info("All indices in ES: " + str(es.indices.get_alias("*")))

  query = {
    "size": k,
    "query": {
      "bool": {
        "filter": {"term": {"tenantId": tenant_id}},
        "must": {
          "script_score": {
            "query": {"match_all": {}},
            "script": {
              "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
              "params": {"query_vector": vector}
            }
          }
        }
      }
    }
  }

  logger.info(
    f"Searching for similar documents in alias '{alias_name}' with query: {query}")

  try:
    response = es.search(index=alias_name, body=query)
    hits = response["hits"]["hits"]
    if not hits:
      print("No results.")
      return []

    results = []

    print("\nTop results:")
    for i, hit in enumerate(hits, 1):
      score = hit['_score']
      text = hit['_source']['text']
      raw_filter = hit['_source']['filter_details']
      filter_data = json.loads(raw_filter)

      # Get type (ensure fallback if missing)
      filter_type = filter_data.get("fieldType")
      operators = operator_mapping.get(filter_type, "No operators defined")

      result = {
        "score": score,
        "text": text,
        "filter_details": filter_data,
        "operators": operators
      }

      print(f"\n{i}. Score: {score:.4f}")
      print(f"Text: {text}")
      print(f"Filter Details: {filter_data}")
      print(f"Operators: {operators}")

      results.append(result)

    return results

  except exceptions.ApiError as e:
    print("Search error:", e.info)
    return []


def search_similar_documents_in_standard_picklist(vector: list[float],
    tenant_id: int,entity:str, k: int = 10):
  if len(vector) > 1024:
    vector = vector[:1024]

  # You can use a list or comma-separated string
  alias_name = f"{tenant_id}-{entity}"
  if entity == "company":
    standard_alias= "standard-picklist-company"
  else:
    standard_alias = "standard-picklist"

  indices = [standard_alias, alias_name]  # <- ADD second index here

  query = {
    "size": k,
    "query": {
      "bool": {
        "filter": {"terms": {"tenantId": [tenant_id, 0]}},
        "must": {
          "script_score": {
            "query": {"match_all": {}},
            "script": {
              "source": "cosineSimilarity(params.query_vector, 'vector') + 1.0",
              "params": {"query_vector": vector}
            }
          }
        }
      }
    }
  }

  logger.info(
    f"Searching for similar documents in indices '{indices}' with query: {query}")

  try:
    response = es.search(index=indices, body=query)  # <--- list of indices
    hits = response["hits"]["hits"]
    if not hits:
      print("No results.")
      return []

    results = []

    print("\nTop results:")
    for i, hit in enumerate(hits, 1):
      score = hit['_score']
      text = hit['_source']['text']
      raw_filter = hit['_source']['filter_details']
      filter_data = json.loads(raw_filter)

      filter_type = filter_data.get("fieldType")
      operators = operator_mapping.get(filter_type, "No operators defined")

      result = {
        "score": score,
        "text": text,
        "filter_details": filter_data,
        "operators": operators
      }

      print(f"\n{i}. Score: {score:.4f}")
      print(f"Text: {text}")
      print(f"Filter Details: {filter_data}")
      print(f"Operators: {operators}")

      results.append(result)

    return results

  except exceptions.ApiError as e:
    print("Search error:", e.info)
    return []

def bulk_index_filters(filters, tenant_id, alias_name, entity_type=None,
    batch_size=100):
  total = len(filters)
  logger.info(
      f"Indexing {total} filters for tenantId={tenant_id} using alias '{alias_name}' in batches of {batch_size}...")

  for i in range(0, total, batch_size):
    batch = filters[i:i + batch_size]
    actions = []

    logger.info(
        f"Processing batch {i // batch_size + 1} with {len(batch)} filters.")

    for f in batch:
      try:
        header = f.get("header", "UNKNOWN")
        tenant_id_item = int(f.get("tenantId", tenant_id))
        filter_id = f.get("filter_id", f"unknown-{i}")

        logger.info(f"Processing filter: {header} (ID: {filter_id})")

        embedding = get_embedding(header)
        if not embedding:
          logger.warning(f"Skipping '{header}' (embedding failed).")
          continue

        doc = {
          "vector": embedding,
          "text": header,
          "entity_type": entity_type,
          "filter_id": filter_id,
          "filter_details": json.dumps(f),
          "tenantId": tenant_id_item
        }

        actions.append({
          "_index": alias_name,
          "_id": filter_id,
          "_source": doc
        })

      except Exception as e:
        logger.error(
            f"Error processing filter ID={f.get('filter_id', 'unknown')}: {e}")

    if actions:
      try:
        success, _ = bulk(es, actions, refresh=False)
        logger.info(
            f"Indexed batch {i // batch_size + 1}: {success} documents.")
      except Exception as e:
        logger.error(
            f"Bulk indexing failed for batch {i // batch_size + 1}: {e}")

  es.indices.refresh(index=alias_name)
  logger.info("All batches indexed and index refreshed.")


def preprocess_filters(data, tenant_id, entity_type):
  try:
    logger.info(
      f"Preprocessing filters for tenantId={tenant_id} and entityType={entity_type}...")
    columns = data["event"]["pageConfig"]["tableConfig"]["columns"]
    logger.info(f"Columns found: {columns}")
    output = []

    for col in columns:
      field_type = col.get("fieldType")
      col_id = col.get("id")
      header = col.get("header")
      is_filterable = col.get("isFilterable", False)

      if (
          field_type in ("PICK_LIST", "MULTI_PICKLIST")
          and isinstance(col.get("picklist"), dict)
          and "picklistValues" in col["picklist"]
      ):
        field = (
          col_id
          if field_type == "PICK_LIST"
          else f"customFieldValues.{col_id}"
        )
        value_type = "string" if col_id in standard_picklist else "long"
        if entity_type == "lead" and col_id in standard_picklist_industry_business_Lead:
          value_type = "string"
        elif entity_type == "company" and col_id in standard_picklist_industry_business_company:
          value_type = "long"

        for item in col["picklist"]["picklistValues"]:
          logger.info(
            f"Processing picklist item: {item['name']} with ID: {item['id']} with col_id: {col_id}")

          if col_id in standard_picklist:
            continue
          output.append(
              {
                "filter_id": tenant_id + "-" + entity_type + "-" + col_id + "-" + str(
                    item["id"]),
                "id": col_id,
                "field": field,
                "type": value_type,
                "valueId": item["id"],
                "value": item["name"],
                "relatedFieldIds": "null",
                "isFilterable": is_filterable,
                "fieldType": field_type,
                "header": f"{header} {item['displayName']}".lower(),
              }
          )

      elif field_type == "DATETIME_PICKER":
        output.append(
            {
              "filter_id": tenant_id + "-" + entity_type + "-" + col_id,
              "id": col_id,
              "field": col_id,
              "type": "date",
              "value": "It should be in ISO 8601 format example 2022-07-13T11:16:45.876+0000",
              "relatedFieldIds": "null",
              "isFilterable": is_filterable,
              "timeZone": "Asia/Calcutta",
              "fieldType": field_type,
              "header": header.lower(),
            }
        )

      elif field_type == "ENTITY_FIELDS":
        output.append(
            {
              "filter_id": tenant_id + "-" + entity_type + "-" + col_id,
              "id": col_id,
              "field": col_id,
              "primaryField": col["primaryField"],
              "property": "teams",
              "type": "long",
              "value": "",
              "relatedFieldIds": "null",
              "isFilterable": is_filterable,
              "fieldType": field_type,
              "header": header.lower(),
            }
        )

      elif field_type == "LOOK_UP":
        output.append(
            {
              "filter_id": tenant_id + "-" + entity_type + "-" + col_id,
              "id": col_id,
              "field": col_id,
              "type": "long",
              "value": "",
              "relatedFieldIds": "null",
              "isFilterable": is_filterable,
              "lookupUrl": col["lookup"]["lookupUrl"],
              "fieldType": field_type,
              "header": header.lower(),
            }
        )

      elif field_type == "PIPELINE":
        output.append(
            {
              "filter_id": tenant_id + "-" + entity_type + "-" + col_id,
              "id": col_id,
              "field": col_id,
              "type": "long",
              "value": "",
              "isFilterable": is_filterable,
              "lookupUrl": col["lookup"]["lookupUrl"],
              "fieldType": field_type,
              "header": header.lower(),
            }
        )
      elif field_type == "PIPELINE_STAGE":
        output.append(
            {
              "filter_id": tenant_id + "-" + entity_type + "-" + col_id,
              "id": col_id,
              "field": col_id,
              "type": "long",
              "value": "",
              "relatedFieldIds": col.get("relatedFieldIds", "null"),
              "isFilterable": is_filterable,
              "lookupUrl": col["lookup"]["lookupUrl"],
              "fieldType": field_type,
              "header": header.lower(),
            }
        )

      elif field_type in type_mapping:
        output.append(
            {
              "filter_id": tenant_id + "-" + entity_type + "-" + col_id,
              "id": col_id,
              "field": col_id,
              "type": type_mapping[field_type],
              "value": static_options.get(field_type, ""),
              "relatedFieldIds": "null",
              "isFilterable": is_filterable,
              "fieldType": field_type,
              "header": header.lower(),
            }
        )

    logger.info(
      f"Processed {len(output)} filters for tenantId={tenant_id} and entityType={entity_type}.")
    return output
  except Exception as e:
    raise HTTPException(
        status_code=400,
        detail="Error processing filters: preprocess_filters " + str(e),
    )


def preprocess_filters_for_standard_picklists(data, tenant_id, entity_type):
  try:
    logger.info(
      f"Preprocessing filters for tenantId={tenant_id} and entityType={entity_type}... and data is {data}")

    output = []

    for col in data:
      logger.info(
        f"Processing column: {col.get('field_display_name')} with ID: {col.get('filter_id')} with col {col}")

      if entity_type=="company":
        value_type="long"
      else:
        value_type="string"

      output.append(
          {
            "filter_id": col.get("filter_id"),
            "id": col.get("field_name"),
            "field": col.get("field_name"),
            "type": value_type,
            "valueId": col.get("picklist_value_id"),
            "value": col.get("picklist_value_name"),
            "relatedFieldIds": "null",
            "isFilterable": col.get("filterable"),
            "fieldType": "PICK_LIST",
            "tenantId": col.get("tenant_id"),
            "header": (col.get("field_display_name") + " " + col.get(
              "picklist_value_display_name")).lower()
          }
      )

    logger.info(
      f"Processed {len(output)} filters for tenantId={tenant_id} and entityType={entity_type}.")
    return output
  except Exception as e:
    raise HTTPException(
        status_code=400,
        detail="Error processing filters: preprocess_filters " + str(e),
    )


def update_filter_in_es(alias_name: str, entity_type: str, filter_id: str,
    new_display_name: str, is_filterable: bool = True):
  try:
    # Get the existing document
    if not es.indices.exists_alias(name=alias_name):
      logger.warning(
        f"Alias '{alias_name}' does not exist. Cannot update filter {filter_id}.")
      return {
        "status": "failure",
        "message": f"Alias '{alias_name}' does not exist",
        "filter_id": filter_id
      }

    response = es.get(index=alias_name, id=filter_id)
    logger.info(
      f"Retrieved filter ID {filter_id} from ES. and response: {response}")
    source = response["_source"]
    logger.info(f"Source data: {source}")
    metadata = json.loads(source["filter_details"])

    # Update metadata fields
    metadata["header"] = new_display_name
    metadata["isFilterable"] = is_filterable

    # Generate new embedding
    new_embedding = get_embedding(new_display_name)
    if not new_embedding:
      logger.info("Failed to generate embedding for new display name.")
      raise Exception("Failed to generate embedding.")

    # Update the document
    updated_doc = {
      "doc": {
        "text": new_display_name,
        "vector": new_embedding,
        "filter_details": json.dumps(metadata),
        "entity_type": entity_type,
        "tenantId": source["tenantId"]
      }
    }

    logger.info("updated_doc: " + str(updated_doc))

    es.update(index=alias_name, id=filter_id, body=updated_doc)
    logger.info(f"Successfully updated filter ID {filter_id} in ES.")

  except exceptions.NotFoundError:
    logger.error(f"Filter ID {filter_id} not found in ES.")
  except Exception as e:
    logger.error(f"Error updating filter in ES: {e}")
    raise HTTPException(
        status_code=400,
        detail=f"Error updating filter in Elasticsearch: {str(e)}"
    )


def get_filter_id_by_prefix_es(alias_name: str, prefix_filter_id: str):
  try:
    # Search using a prefix on a keyword field like `filter_id`, not `_id`
    query = {
      "query": {
        "prefix": {
          "filter_id": {
            "value": prefix_filter_id
          }
        }
      }
    }

    response = es.search(index=alias_name, body=query)
    logger.info(f"Response from Elasticsearch: {response}")
    hits = response["hits"]["hits"]
    logger.info(f"Found {hits} hits for prefix '{prefix_filter_id}'.")

    if not hits:
      raise HTTPException(
          status_code=404,
          detail="Filter ID not found in Elasticsearch index.",
      )

    # Return list of filter_ids or documents, depending on your use case
    return [hit["_id"] for hit in hits]

  except Exception as e:
    logger.error(
      f"Error retrieving data from Elasticsearch: get_filter_id_by_prefix_es {e}")
    raise HTTPException(
        status_code=400,
        detail=f"Error retrieving data from Elasticsearch: get_filter_id_by_prefix_es {e}",
    )


def get_filters_by_ids_for_picklist_values_es(filter_ids: list[str],
    alias_name: str):
  try:
    response = es.mget(
        index=alias_name,
        body={"ids": filter_ids}
    )

    found_docs = {}
    for doc in response.get("docs", []):
      if doc.get("found"):
        found_docs[doc["_id"]] = doc["_source"]

    if not found_docs:
      raise HTTPException(
          status_code=404,
          detail="No filter IDs found in Elasticsearch index.",
      )

    return found_docs

  except Exception as e:
    logger.error(
        f"Error retrieving data from Elasticsearch: get_filters_by_ids_for_picklist_values {e}"
    )
    raise HTTPException(
        status_code=400,
        detail=f"Error retrieving data from Elasticsearch: {str(e)}",
    )


def update_multiple_filters_in_vectordb_es(
    namespace: str,
    entity_type: str,
    filter_ids,
    new_display_name: str,
    isFilterable: Optional[bool] = True,
):
  try:
    logger.info("Updating multiple filters in Elasticsearch...")

    alias_name = f"{namespace}-{entity_type}"

    if not es.indices.exists_alias(name=alias_name):
      logger.warning(
        f"Alias '{alias_name}' does not exist. Cannot update filters {filter_ids}.")
      return {
        "status": "failure",
        "message": f"Alias '{alias_name}' does not exist",
        "filter_ids": filter_ids
      }

    existing_picklist_values = get_filters_by_ids_for_picklist_values_es(
        filter_ids=filter_ids, alias_name=alias_name
    )

    logger.info(
        f"Fetched {len(existing_picklist_values)} existing picklist values for update."
    )

    actions = []

    for picklist_value in existing_picklist_values.values():
      internal_metadata = json.loads(picklist_value["filter_details"])
      internal_metadata["header"] = (
          new_display_name
          + " " + picklist_value["text"].split()[-1]
      )
      logger.info(f"Updating header to: {internal_metadata['header']}")
      internal_metadata["isFilterable"] = isFilterable

      doc = {
        "filter_id": picklist_value["filter_id"],
        "text": internal_metadata["header"],
        "entity_type": entity_type,
        "filter_details": json.dumps(internal_metadata),
      }

      logger.info(
        f"Preparing to update filter ID with new display name: {doc['text']} and doc is {doc}")

      actions.append({
        "_op_type": "update",
        "_index": alias_name,
        "_id": picklist_value["filter_id"],
        "doc": doc,
        "doc_as_upsert": True
      })

    logger.info(
        f"Prepared {actions} actions for bulk update in Elasticsearch."
    )
    helpers.bulk(es, actions)
    logger.info(
        f"Updated filters in Elasticsearch for tenant {namespace} and entity type {entity_type}."
    )

  except Exception as e:
    logger.error(
        f"Error updating data in Elasticsearch: update_multiple_filters_in_vectordb {e}"
    )
    raise HTTPException(
        status_code=400,
        detail="Error updating data in Elasticsearch: update_multiple_filters_in_vectordb " + str(
          e),
    )


def update_filter_when_lead_picklist_value_changed_in_es(
    namespace: str, entity_type: str, filter_id: str, new_display_name: str
):
  try:
    logger.info(
      f"Updating filter when lead picklist value changed in Elasticsearch for tenant {namespace} and entity type {entity_type}...")
    print(f"filter_id: {filter_id}")
    index_name = f"{namespace}-{entity_type}"

    if not es.indices.exists_alias(name=index_name):
      logger.warning(
        f"Alias '{index_name}' does not exist. Cannot update filter {filter_id}.")
      return {
        "status": "failure",
        "message": f"Alias '{index_name}' does not exist",
        "filter_id": filter_id
      }

    # Fetch the existing document
    existing_filter_response = get_filter_by_id(filter_id=filter_id,
                                                index_name=index_name)

    # Parse and update metadata
    logger.info(f"Existing filter: {existing_filter_response}")
    existing_filter = existing_filter_response.get("_source")
    internal_metadata = json.loads(existing_filter["filter_details"])

    logger.info(f"Existing filter details: {internal_metadata}")
    internal_metadata["header"] = new_display_name.lower()

    logger.info(
      f"Updating header to: {internal_metadata['header']} and internal_metadata: {internal_metadata}")

    # Prepare the updated document
    updated_doc = {
      "doc": {
        "text": internal_metadata["header"],
        "entity_type": entity_type,
        "filter_details": json.dumps(internal_metadata),
      }
    }

    logger.info(
      f"Updating filter ID {filter_id} with new display name: {new_display_name} and updated_doc: {updated_doc}")

    # Send update to Elasticsearch
    es.update(index=index_name, id=filter_id, body=updated_doc)

    logger.info(
        f"Updated filter {filter_id} successfully in Elasticsearch for tenant {namespace} and entity type {entity_type}."
    )

  except Exception as e:
    logger.error(
        f"Error updating data in Elasticsearch: update_filter_when_lead_picklist_value_changed_in_es {e}"
    )
    raise HTTPException(
        status_code=400,
        detail="Error updating data in Elasticsearch: update_filter_when_lead_picklist_value_changed_in_es "
               + str(e),
    )


def get_filter_by_id(filter_id: str, index_name: str):
  try:
    logger.info(
      f"Retrieving filter by ID: {filter_id} from index: {index_name}")
    response = es.get(index=index_name, id=filter_id)
    logger.info(
      f"Retrieved filter ID {filter_id} from Elasticsearch: {response}")
    return response

  except Exception as e:
    logger.error(
        f"Error retrieving data from Elasticsearch: get_filter_by_id {e}"
    )
    raise HTTPException(
        status_code=400,
        detail="Error retrieving data from Elasticsearch: get_filter_by_id " + str(
            e),
    )
