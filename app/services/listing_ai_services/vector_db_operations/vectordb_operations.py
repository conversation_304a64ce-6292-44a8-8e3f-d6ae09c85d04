# import ast
# import json
# from typing import List, Optional, Dict, Any
#
# from app.services.listing_ai_services.vector_db_operations.elastic_vector_db_operations import \
#     get_filters_by_ids_for_picklist_values_es
# from app.services.logger_config import logger
# from fastapi import HTTPException
# from pinecone import Pinecone
# import os
# from dotenv import load_dotenv
# from pinecone import ServerlessSpec
# import requests
#
# load_dotenv()
#
# pinecone_api_key = os.getenv("PINECONE_API_KEY")
# pinecone_index_name = os.getenv("PINECONE_INDEX_NAME")
# pinecone_index_host = os.getenv("PINECONE_INDEX_HOST")
#
# pc = Pinecone(api_key=pinecone_api_key)
#
# index = pc.Index(pinecone_index_name)
#
#
# def creteate_vectordb_index(index_name: str):
#     try:
#         if not pc.list_indexes():
#             pc.create_index(
#                 name=index_name,
#                 dimension=1536,
#                 metric="cosine",
#                 spec=ServerlessSpec(cloud="aws", region="us-east-1"),
#                 deletion_protection="enabled",
#             )
#             print(f"Index {index_name} created successfully.")
#         else:
#             print(f"Index {index_name} already exists.")
#     except Exception as e:
#         raise HTTPException(
#             status_code=400,
#             detail="Error creating Pinecone index: create_vectordb_index " + str(e),
#         )
#
#
# def delete_vectordb_index(index_name: str):
#     try:
#         pc.delete_index(index_name)
#         print(f"Index {index_name} deleted successfully.")
#     except Exception as e:
#         raise HTTPException(
#             status_code=400,
#             detail="Error deleting Pinecone index: delete_vectordb_index " + str(e),
#         )
#
#
# def insert_data_in_vectordb(
#     namespace: str, entity_type: str, data: List[Dict[str, Any]]
# ):
#     try:
#         batch_size = 95
#         vectors = [
#             {
#                 "id": text["filter_id"],
#                 "text": text["header"],
#                 "entity_type": entity_type,
#                 "filter_details": json.dumps(text),
#             }
#             for i, text in enumerate(data)
#         ]
#
#         for i in range(0, len(vectors), batch_size):
#             batch = vectors[i : i + batch_size]
#             index.upsert_records(records=batch, namespace=namespace)
#             print(f"Upserted batch {i // batch_size + 1} of size {len(batch)}")
#
#         print("All data inserted successfully.")
#         logger.info(
#             f"Inserted data in vectordb for tenant {namespace} and entity type {entity_type}."
#         )
#
#     except Exception as e:
#         logger.error(
#             f"Error inserting data in Pinecone: insert_data_in_vectordb {e}"
#         )
#         raise HTTPException(
#             status_code=400,
#             detail="Error inserting data into Pinecone: insert_data_in_vectordb "
#             + str(e),
#         )
#
#
# def update_filters_in_vectordb(
#     namespace: str, entity_type: str, filter_id: str, new_display_name: str, isFilterable: Optional[bool] = True
# ):
#     try:
#         print(f"filter_id: {filter_id}")
#         existing_filter = get_filter_by_id(namespace=namespace, filter_id=filter_id)
#         internal_metadata = json.loads(existing_filter.metadata["filter_details"])
#         internal_metadata["header"] = new_display_name
#         internal_metadata["isFilterable"] = isFilterable
#
#         index.upsert_records(
#             records=[
#                 {
#                     "id": filter_id,
#                     "text": internal_metadata["header"],
#                     "entity_type": entity_type,
#                     "filter_details": json.dumps(internal_metadata),
#                 }
#             ],
#             namespace=namespace,
#         )
#
#         print(f"Updated filter {filter_id} successfully.")
#
#     except Exception as e:
#         print("Error updating data in Pinecone: update_filters_in_vectordb " + str(e))
#         raise HTTPException(
#             status_code=400,
#             detail="Error updating data in Pinecone: update_filters_in_vectordb "
#             + str(e),
#         )
#
# def update_filter_when_lead_picklist_value_changed_in_vectordb(
#     namespace: str, entity_type: str, filter_id: str, new_display_name: str
# ):
#     try:
#         print(f"filter_id: {filter_id}")
#         existing_filter = get_filter_by_id(namespace=namespace, filter_id=filter_id)
#         internal_metadata = json.loads(existing_filter.metadata["filter_details"])
#         internal_metadata["header"] = new_display_name
#
#         index.upsert_records(
#             records=[
#                 {
#                     "id": filter_id,
#                     "text": internal_metadata["header"],
#                     "entity_type": entity_type,
#                     "filter_details": json.dumps(internal_metadata),
#                 }
#             ],
#             namespace=namespace,
#         )
#
#         logger.info(
#             f"Updated filter {filter_id} successfully in vectordb for tenant {namespace} and entity type {entity_type}."
#         )
#
#     except Exception as e:
#         logger.error(
#             f"Error updating data in Pinecone: update_filter_when_lead_picklist_value_changed_in_vectordb {e}"
#         )
#         raise HTTPException(
#             status_code=400,
#             detail="Error updating data in Pinecone: update_filter_when_lead_picklist_value_changed_in_vectordb "
#             + str(e),
#         )
#
#
# def delete_filters_in_vectordb(namespace: str, filter_id: str):
#     try:
#         if filter_id not in index.list(namespace=namespace):
#             raise HTTPException(
#                 status_code=404,
#                 detail="Filter ID not found in Pinecone index.",
#             )
#
#         index.delete(ids=[filter_id], namespace=namespace)
#         print("Data deleted successfully.")
#         return True
#     except Exception as e:
#         return False
#
#
# def get_filters_from_vectordb(user_query: str, namespace: str, entity_type: str):
#     try:
#         logger.info(f"Searching vectordb for query: '{user_query}' in namespace: '{namespace}' with entity_type: '{entity_type}'")
#
#         # Search without filter first to see what's happening
#         results = index.search(
#             namespace=namespace,
#             query={
#                 "inputs": {"text": f"{user_query}"},
#                 "top_k": 50,
#                 # "filter": filter_condition
#             },
#             fields=["filter_details", "text", "isFilterable"],
#             rerank={"model": "bge-reranker-v2-m3", "rank_fields": ["text"]},
#         )
#
#         logger.info(f"Searched vectordb for results: '{results}'")
#
#         total_hits = len(results["result"]["hits"]) if "result" in results and "hits" in results["result"] else 0
#         logger.info(f"Vectordb search returned {total_hits} total hits for entity_type: {entity_type}")
#
#         chunk_texts = []
#         for i, hit in enumerate(results["result"]["hits"][:5]):
#             data = json.loads(hit["fields"]["filter_details"])
#
#             # Check if the filter_id starts with the namespace-entity_type pattern
#             filter_id = data.get("filter_id", "")
#             expected_prefix = f"{namespace}-{entity_type}-"
#
#             if not filter_id.startswith(expected_prefix):
#                 logger.info(f"Skipping hit #{i+1} with filter_id={filter_id}, doesn't match prefix {expected_prefix}")
#                 continue
#
#             is_filterable = data.get("isFilterable", False)
#             logger.info(f"Hit #{i+1}: id={filter_id}, score={hit.get('score', 'unknown')}, "
#                        f"field={data.get('field', 'unknown')}, isFilterable={is_filterable}")
#
#             if is_filterable:
#                 chunk_texts.append(data)
#                 logger.info(f"Added filterable field: {data.get('header', 'unknown')}")
#             else:
#                 logger.info(f"Skipped non-filterable field: {data.get('header', 'unknown')}")
#
#         logger.info(f"Returning {len(chunk_texts)} filterable fields from vectordb for entity_type: {entity_type}")
#         return chunk_texts
#     except Exception as e:
#         logger.error(
#             f"Error retrieving data from Pinecone: get_filters_from_vectordb {e}"
#         )
#         raise HTTPException(
#             status_code=400,
#             detail="Error retrieving data from Pinecone: get_filters_from_vectordb "
#             + str(e),
#         )
#
#
# def get_filter_by_id(filter_id: str, namespace: str):
#     try:
#         result = index.fetch(ids=[filter_id], namespace=namespace)
#         if not result:
#             raise HTTPException(
#                 status_code=404,
#                 detail="Filter ID not found in Pinecone index.",
#             )
#         return result.vectors[filter_id]
#     except Exception as e:
#         logger.error(
#             f"Error retrieving data from Pinecone: get_filter_by_id {e}"
#         )
#         raise HTTPException(
#             status_code=400,
#             detail="Error retrieving data from Pinecone: get_filter_by_id " + str(e),
#         )
#
#
# def get_filter_by_id_for_picklist_values(filter_id, namespace: str):
#     try:
#         result = index.fetch(ids=filter_id, namespace=namespace)
#         if not result:
#             raise HTTPException(
#                 status_code=404,
#                 detail="Filter ID not found in Pinecone index.",
#             )
#         return result.vectors
#     except Exception as e:
#         logger.error(
#             f"Error retrieving data from Pinecone: get_filter_by_id_for_picklist_values {e}"
#         )
#         raise HTTPException(
#             status_code=400,
#             detail="Error retrieving data from Pinecone: get_filter_by_id_for_picklist "
#             + str(e),
#         )
#
#
# def get_filter_id_by_prefix(prefix_filter_id: str, namespace: str):
#     try:
#         result = index.list(prefix=prefix_filter_id, namespace=namespace)
#         if not result:
#             raise HTTPException(
#                 status_code=404,
#                 detail="Filter ID not found in Pinecone index.",
#             )
#
#         result = [item for sublist in result for item in sublist]
#
#         return result
#     except Exception as e:
#         logger.error(
#             f"Error retrieving data from Pinecone: get_filter_id_by_prefix {e}"
#         )
#         raise HTTPException(
#             status_code=400,
#             detail="Error retrieving data from Pinecone: get_filter_id_by_prefix "
#             + str(e),
#         )
#
#
# ###################################################################
#
#
# def update_multiple_filters_in_vectordb(
#     namespace: str, entity_type: str, filter_ids, new_display_name: str, isFilterable: Optional[bool] = True
# ):
#     try:
#
#         existing_picklist_values = get_filter_by_id_for_picklist_values(
#             filter_id=filter_ids, namespace=namespace
#         )
#
#         updated_records_to_upsert = []
#
#         for picklist_value in existing_picklist_values.values():
#             internal_metadata = json.loads(picklist_value.metadata["filter_details"])
#             internal_metadata["header"] = (
#                 new_display_name
#                 + " is "
#                 + picklist_value.metadata["text"].split("is")[-1].strip()
#             )
#             internal_metadata["isFilterable"] = isFilterable
#
#             updated_records_to_upsert.append(
#                 {
#                     "id": picklist_value.id,
#                     "text": internal_metadata["header"],
#                     "entity_type": entity_type,
#                     "filter_details": json.dumps(internal_metadata),
#                 }
#             )
#
#         batch_size = 95
#
#         for i in range(0, len(updated_records_to_upsert), batch_size):
#             batch = updated_records_to_upsert[i : i + batch_size]
#             index.upsert_records(records=batch, namespace=namespace)
#             print(f"Upserted batch {i // batch_size + 1} of size {len(batch)}")
#
#         logger.info(
#             f"Updated filters in vectordb for tenant {namespace} and entity type {entity_type}."
#         )
#
#     except Exception as e:
#         logger.error(
#             f"Error updating data in Pinecone: update_multiple_filters_in_vectordb {e}"
#         )
#
#         raise HTTPException(
#             status_code=400,
#             detail="Error updating data in Pinecone: update_multiple_filters_in_vectordb "
#             + str(e),
#         )
#
# def delete_namespace_from_vectordb(ids, namespace: str):
#     try:
#         index.delete(ids=ids, namespace=namespace)
#         logger.info(
#             f"Deleted namespace {namespace} from Pinecone index {pinecone_index_name}."
#         )
#         print(f"Namespace {namespace} deleted successfully.")
#         return True
#     except Exception as e:
#         logger.error(
#             f"Error deleting namespace from Pinecone: delete_namespace_from_vectordb {e}"
#         )
#         raise HTTPException(
#             status_code=400,
#             detail="Error deleting namespace from Pinecone: delete_namespace_from_vectordb "
#             + str(e),
#         )
#
# def get_all_namespaces_from_vectordb():
#     try:
#         response = index.describe_index_stats()
#         namespaces = list(response.get("namespaces", {}).keys())
#
#         logger.info(
#             f"Fetched all namespaces from Pinecone index: {namespaces}."
#         )
#         return namespaces
#     except Exception as e:
#         logger.error(
#             f"Error fetching all namespaces from Pinecone: get_all_namespaces_from_vectordb {e}"
#         )
#         raise HTTPException(
#             status_code=400,
#             detail="Error fetching all namespaces from Pinecone: get_all_namespaces_from_vectordb "
#             + str(e),
#         )
#
# def namespace_exists_in_vectordb(namespace: str):
#     try:
#         response = index.describe_index_stats()
#         namespaces = response.get("namespaces", {})
#         exists = namespace in namespaces
#
#         logger.info(
#             f"Checked if namespace {namespace} exists in Pinecone index: {exists}"
#         )
#         return exists
#     except Exception as e:
#         logger.error(
#             f"Error checking namespace in Pinecone: namespace_exists_in_vectordb {e}"
#         )
#         raise HTTPException(
#             status_code=400,
#             detail="Error checking namespace in Pinecone: namespace_exists_in_vectordb "
#             + str(e),
#         )
#
