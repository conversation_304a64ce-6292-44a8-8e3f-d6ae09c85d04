import logging
import os
from email.policy import default

import requests
from dotenv import load_dotenv
from app.services.logger_config import logger

load_dotenv()

base_url = os.getenv("IAM_BASE_PATH")


def lookup_user(bearer_token, url:str, first_name: str):
    logger.info(f"Looking up user with firstName={first_name}")
    url = f"{base_url}/v1/users/lookup?q=firstName:"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
    }

    logger.info(f"Making request to {url}")
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        user_data = response.json()
        logger.info(f"Successfully retrieved user data. Found {len(user_data.get('content', []))} users")
        return user_data
    else:
        logger.error(f"Failed to lookup user: status_code={response.status_code}, response={response.text}")
        raise Exception(f"Error: {response.status_code} - {response.text}")



def lookup_pipeline(bearer_token):
    url = f"http://sd-sales/v1/pipelines/lookup?entityType=LEAD&q=name:"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
    }

    response = requests.get(url, headers=headers)


    if response.status_code != 200:
        raise Exception(f"Error: {response.status_code} - {response.text}")

    data = response.json()
    pipelines = data.get("content", [])

    if not pipelines:
        return None

    # Normalize names
    for pipeline in pipelines:
        pipeline["name_lower"] = pipeline["name"].lower()

    # Step 1: Exact match
    for pipeline in pipelines:
        if pipeline["name_lower"] == "default lead pipeline":
            return pipeline["id"]

    # Step 2: Contains "default"
    default_matches = [p for p in pipelines if "default" in p["name_lower"]]
    if default_matches:
        return default_matches[0]["id"]

    # Step 3: First available
    return pipelines[0]["id"]


def get_pipeline(bearer_token, entity_type="LEAD"):
    entity_type = entity_type.upper()
    url = f"http://sd-sales/v1/pipelines/lookup?entityType={entity_type}&q=name:"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
    }

    response = requests.get(url, headers=headers)

    if response.status_code != 200:
        raise Exception(f"Error: {response.status_code} - {response.text}")

    data = response.json()
    return data.get("content", [])


def get_pipeline_stages(bearer_token: str, pipeline_id: int):
    logger.info(f"Fetching pipeline stages for pipeline_id={pipeline_id}, bearer_token={bearer_token}")
    url = f"http://sd-sales/v1/pipelines/{pipeline_id}/stages"
    headers = {
        "Authorization": f"Bearer {bearer_token}"
    }

    response = requests.get(url, headers=headers)

    logger.info(f"Pipeline stages response: {response.json()}")
    if response.status_code == 200:
        return response.json()# assuming similar response format
    else:
        raise Exception(f"Failed to fetch stages: {response.status_code} - {response.text}")


def lookup_products(bearer_token, product_name: str = ""):
    url = f"http://sd-product/v1/products/lookup?q=name:{product_name}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
    }

    logger.info(f"Looking up products with name={product_name}")
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        product_data = response.json()
        logger.info(f"Successfully retrieved product data. Found {len(product_data.get('content', []))} products")
        return product_data.get("content", [])
    else:
        logger.error(f"Failed to lookup products: status_code={response.status_code}, response={response.text}")
        raise Exception(f"Error: {response.status_code} - {response.text}")


def lookup_companies(bearer_token, company_name: str = ""):
    url = f"http://sd-company/v1/companies/lookup?q=name:{company_name}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
    }

    logger.info(f"Looking up companies with name={company_name}")
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        company_data = response.json()
        logger.info(f"Successfully retrieved company data. Found {len(company_data.get('content', []))} companies")
        return company_data.get("content", [])
    else:
        logger.error(f"Failed to lookup companies: status_code={response.status_code}, response={response.text}")
        raise Exception(f"Error: {response.status_code} - {response.text}")


def lookup_contacts(bearer_token, first_name: str = ""):
    url = f"http://sd-search/v1/search/contact/lookup?q=firstName:"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
    }

    logger.info(f"Looking up contacts with firstName={first_name}")
    response = requests.get(url, headers=headers)
    logger.info(f"Response is at 146: {response.json()}")

    if response.status_code == 200:
        contact_data = response.json()
        logger.info(f"Successfully retrieved contact data. Found {len(contact_data.get('content', []))} contacts")
        return contact_data.get("content", [])
    else:
        logger.error(f"Failed to lookup contacts: status_code={response.status_code}, response={response.text}")
        raise Exception(f"Error: {response.status_code} - {response.text}")
