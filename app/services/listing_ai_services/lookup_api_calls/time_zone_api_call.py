import os
import requests
from dotenv import load_dotenv
from app.services.logger_config import logger

load_dotenv()

base_url = os.getenv("IAM_BASE_PATH")

def get_time_zone(bearer_token):
    url = f"{base_url}/v1/users/me"

    logger.info(f"Fetching user timezone from: {url}")

    headers = {
        "Authorization": f"Bearer {bearer_token}",
    }

    try:
        logger.info("Making request to user API")
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            user_data = response.json()
            timezone = user_data.get("timezone")
            logger.info(f"Successfully retrieved user timezone: {timezone}")
            return timezone
        else:
            logger.error(f"Failed to get timezone: status_code={response.status_code}, response={response.text}")
            raise Exception(f"Error: {response.status_code} - {response.text}")
    except Exception as e:
        logger.exception(f"Exception occurred while fetching timezone: {str(e)}")
        raise


