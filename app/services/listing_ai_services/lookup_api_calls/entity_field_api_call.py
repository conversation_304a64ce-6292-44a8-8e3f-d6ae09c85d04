import os
import requests
from dotenv import load_dotenv

load_dotenv()

base_url = os.getenv("IAM_BASE_PATH")

def entity_field_data_extractor(name, bearer_token):
    url = f"{base_url}/v1/teams/lookup?q=name:{name}"

    headers = {
        "Authorization": f"Bearer {bearer_token}",
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        return response.json()  
    else:
        raise Exception(f"Error: {response.status_code} - {response.text}")


def get_entity_labels(bearer_token):
    url = "http://sd-config/v1/entities/label"

    headers = {
        "Authorization": f"Bearer {bearer_token}",
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Error: {response.status_code} - {response.text}")



