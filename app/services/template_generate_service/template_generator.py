import json
import os
from datetime import date, datetime, timezone
from typing import Optional

from dotenv import load_dotenv
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from openai import OpenAI
from sqlalchemy.orm import Session

from app.db.repository.request_history_repository import RequestHistoryService
from app.db.repository.token_history_repository import TokenHistoryService
from app.db.repository.users_repository import UserService
from app.exceptions import TokenLimitExceededException
from app.rabbitmq.config import logger
from app.services.template_generate_service.template_models import TemplateResponseFormat, TemplateRequestFormat
from app.utils.app_utils import AppUtils
from app.utils.error_handler import ErrorHandler

class TemplateGenerator:
    def __init__(self,db_session: Session):
        try:
            self.db_session = db_session
            self._init_env()
            self._init_services()
        except Exception as e:
            logger.error(
                "Failed to initialize Template Generator",
                exc_info=e
            )

    def _init_env(self):
        try:
            load_dotenv()
            api_key = os.getenv("OPENAI_API_KEY")
            daily_token_limit = int(os.getenv("DAILY_TOKEN_LIMIT"))
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable is not set")
            if not daily_token_limit:
                raise ValueError("DAILY_TOKEN_LIMIT environment variable is not set")
            self.client = OpenAI(api_key=api_key)
            self.daily_token_limit = daily_token_limit
        except Exception as e:
            logger.error(
                "Failed to initialize OpenAI client",
                exc_info=e
            )

    def _init_services(self):
        try:
            self.request_history_service = RequestHistoryService(session=self.db_session)
            self.token_history_service = TokenHistoryService(session=self.db_session)
            self.user_service = UserService(session=self.db_session)
        except Exception as e:
            logger.error(
                "Failed to initialize services",
                exc_info=e
            )

    def _validate_user_exists(self, user_id : str,token : str):
        try:
            return AppUtils.get_user(user_id=user_id, token=token, user_service=self.user_service)
        except Exception as e:
            logger.error(
                "Failed to validate user",
                exc_info=e
            )

    async def _generate_content(
            self,
            request_prompt,
            user_id,
            tenant_id,
            template_request : TemplateRequestFormat,
            old_template_id : Optional[int] = None,
    ) -> Optional[TemplateResponseFormat]:
        try:
            logger.info(
                f"{'Rewriting' if old_template_id is not None else 'Generating'} content for user {user_id} , "
                f"{f'(old_template : {old_template_id})'if old_template_id is not None else ''}."
            )

            chat_completion = self.client.beta.chat.completions.parse(
                messages=request_prompt,
                model='gpt-4o-mini',
                response_format=TemplateResponseFormat,
            )

            input_token = chat_completion.usage.prompt_tokens
            output_token = chat_completion.usage.completion_tokens
            total_token_consumed = chat_completion.usage.total_tokens
            generated_template = chat_completion.choices[0].message.parsed

            # update usage
            self.token_history_service.update_or_insert_token_history(
                user_id=user_id,
                tenant_id=tenant_id,
                token_limit=self.daily_token_limit,
                token_consumed=total_token_consumed,
                usage_date=date.today(),
            )
            # save request
            record = self.request_history_service.insert_request(
                user_id=user_id,
                tenant_id=tenant_id,
                requested_input=template_request.model_dump_json(),
                generated_response={
                    "content": generated_template.content
                },
                request_date=datetime.now(timezone.utc),
                input_token_consumed=input_token,
                output_token_consumed=output_token,
                total_consumed=total_token_consumed,
                response_accepted_status=False,
                email_id=None,
                entity_type="whatsapp_template",
                operation=f"{'rewrite' if old_template_id is not None else 'generate'}",
                entity_id=old_template_id
            )
            logger.info(
                f"Successfully generated content for user {user_id} , template_id : {record.id}"
            )
            return TemplateResponseFormat(
                id=str(record.id),
                content=generated_template.content,
            )

        except Exception as e:
            ErrorHandler.handle_error(exception=e)


    async def create_template(
            self,
            tenant_id: str,
            user_id: str,
            token : str,
            template_request : TemplateRequestFormat
    ) -> Optional[TemplateResponseFormat]:
        """
        Generate whatsapp template from given instructions
        Args:
            tenant_id (str): Tenant ID.
            user_id (int): ID of the user to whom the task is assigned.
            token (str): Authentication token.
            template_request (TemplateRequestFormat): contains instructions for prompt
        Returns:
            Optional[TemplateResponseFormat]: Returns the generated template.
        """
        try:
            await self._validate_user_exists(user_id=user_id, token=token)

            # check if token limit exceeded
            if self.token_history_service.is_token_limit_exceeded(
                user_id=user_id,
                tenant_id=tenant_id,
                usage_date=date.today()
            ):
                raise TokenLimitExceededException()

            response_schema = json.dumps(
                TemplateResponseFormat.model_json_schema(),
                indent=2
            )

            request_prompt = [
                {
                    "role": "system",
                    "content": (
                        f"You are an expert in creating whatsapp templates for businesses"
                        f"Follow these instructions carefully : {template_request.instructions}"
                        f"While adding variables, wrap them in double curly braces and use natural numbers for variable name"
                        f"Ensure that the generated JSON strictly adheres to the following schema:\n{response_schema}"
                        f"Also make sure that meta policies are not violated when using this template"
                    )
                }
            ]

            return await self._generate_content(
                request_prompt=request_prompt,
                user_id=user_id,
                tenant_id=tenant_id,
                template_request=template_request,
            )
        except Exception as e:
            ErrorHandler.handle_error(exception=e)


    async def rewrite_template(
            self,
            template_id: int,
            tenant_id: str,
            user_id: str,
            token : str,
            template_request : TemplateRequestFormat
    ) -> Optional[TemplateResponseFormat]:
        """
        Rewrite whatsapp template from given instructions using old template
        Args:
            template_id (int): ID of the template to rewrite.
            tenant_id (str): Tenant ID.
            user_id (int): ID of the user to whom the task is assigned.
            token (str): Authentication token.
            template_request (TemplateRequestFormat): contains instructions for prompt
        Returns:
            Optional[TemplateResponseFormat]: Returns the generated template.
        """
        try:
            await self._validate_user_exists(user_id=user_id, token=token)

            if self.token_history_service.is_token_limit_exceeded(
                user_id=user_id,
                tenant_id=tenant_id,
                usage_date=date.today()
            ):
                raise TokenLimitExceededException()

            previous_template = self.request_history_service.get_request_data_by_id(request_id=template_id)

            if previous_template is None:
                raise HTTPException(
                    status_code=404,
                    detail="Template not found"
                )

            old_template_content = previous_template["generated_response"]["content"]
            response_schema = json.dumps(TemplateResponseFormat.model_json_schema(),indent=2)
            request_prompt = [
                {
                    "role": "system",
                    "content": (
                        f"You are an expert in re-creating whatsapp templates for businesses"
                        f"Here is the previous template content is : \n{old_template_content}"
                        f"Follow these instructions carefully : {template_request.instructions}"
                        f"While adding variables, wrap them in double curly braces and use natural numbers for variable name"
                        f"Ensure that the generated JSON strictly adheres to the following schema:\n{response_schema}"
                        f"Also make sure that meta policies are not violated when using this template"
                    )
                }
            ]

            return await self._generate_content(
                request_prompt=request_prompt,
                user_id=user_id,
                tenant_id=tenant_id,
                template_request=template_request,
                old_template_id=template_id,
            )
        except Exception as e:
            ErrorHandler.handle_error(exception=e)

    async def mark_template_as_accepted(
            self,
            template_id: int,
            user_id: str,
            token: str,
    ) -> Optional[bool]:
        try:
            await self._validate_user_exists(user_id=user_id, token=token)
            logger.info(
                f"Marking template {template_id} as accepted by user {user_id}"
            )
            self.request_history_service.update_response_status(
                request_id=template_id,
                status=True
            )
            logger.info(
                f"Successfully marked template {template_id} as accepted by user {user_id}"
            )
            return True

        except Exception:
            raise