import json
import os
from datetime import datetime, date, timezone
from typing import List, Optional, Dict, Any

import requests
from dotenv import load_dotenv
from fastapi import HTTPException
from openai import OpenAI
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import N<PERSON><PERSON><PERSON>
from sqlalchemy import Column

from app.db.repository.request_history_repository import RequestHistoryService
from app.db.repository.token_history_repository import TokenHistoryService
from app.db.repository.users_repository import UserService  # type: ignore
from app.exceptions import TokenLimitExceededException
from .email_models import EmailOutputFormat, NewEmailRequestFormat
from app.services.logger_config import logger

load_dotenv()
DATABASE_URL = os.getenv("DATABASE_URL")
daily_token_limit = os.getenv("DAILY_TOKEN_LIMIT")

# Create an engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(bind=engine)
session = SessionLocal()

request_history_service = RequestHistoryService(session)
token_history_service = TokenHistoryService(session)
user_service = UserService(session)


class EmailWriter:
  def __init__(self):
    try:
      load_dotenv()
      api_key = os.getenv("OPENAI_API_KEY")
      if not api_key:
        raise ValueError("OPENAI_API_KEY not found in environment variables")
      self.client = OpenAI(api_key=api_key)
    except Exception as e:
      logger.error(
          "Failed to initialize EmailResponseHandler",
          exc_info=e  # This includes the full stack trace of the exception
      )
      raise

  async def get_reply_email_response(
      self,
      prev_email_list: List[Dict[str, Any]],
      tenant_id,
      user_id,
      request: NewEmailRequestFormat,
      token: str,
      db_session,
      system_prompt: Optional[str] = None,
  ) -> EmailOutputFormat:
    try:

      request_history_service = RequestHistoryService(db_session)
      token_history_service = TokenHistoryService(db_session)
      user_service = UserService(db_session)

      existing_user = user_service.get_user_by_id(user_id)
      if not existing_user:
        iam_user = self.get_user_from_iam(user_id, token)
        if iam_user:

          existing_user = user_service.insert_user(
              id=iam_user["id"],
              name=iam_user["name"],
              email=iam_user["email"],
              tenant_id=iam_user["tenant_id"]
          )
          logger.info(
              f"Inserted new user from IAM - user_id={existing_user.id}"
          )
        else:
          logger.error(
              f"User not found in IAM - user_id={user_id}"
          )
          raise HTTPException(status_code=400,
                              detail="User not found")
      # Check Token Limit
      if token_history_service.is_token_limit_exceeded(
          user_id=user_id, tenant_id=tenant_id, usage_date=date.today()
      ):
        logger.info(
            f"User exceeded token limit - user_id={user_id}, tenant_id={tenant_id}"
        )
        raise TokenLimitExceededException(
            "Token limit exceeded. Please upgrade your plan."
        )

      if not prev_email_list:
        raise HTTPException(
            status_code=400, detail="Previous email list cannot be empty"
        )

      # LLM Schema Definition
      schema_str = json.dumps(EmailOutputFormat.model_json_schema(), indent=2)

      email_recipient_name = f"email recipient name is {request.to[0].name}" if request.to else ""
      email_sender_name = f"email sender name is {existing_user.name}" if existing_user.name else ""

      # Construct LLM Prompt
      llm_prompt = (
        "You are an expert email assistant. Your task is to generate a professional and context-aware email response "
        f"based on the previous email conversation history provided below. \n {email_recipient_name} \n {email_sender_name}\n"
        "Ensure that your response follows a structured JSON format, strictly adhering to this schema:\n"
        f"{schema_str}\n"
        "Guidelines:\n"
        "- Keep the response concise and to the point.\n"
        "- Maintain a polite and professional tone.\n"
        "- Ensure the response is relevant to the latest email in the conversation.\n"
        f"Custom Instructions: {system_prompt}\n"
        f"Previous Email Conversations: {prev_email_list}"
      )

      # **Save request details FIRST**
      inserted_data = request_history_service.insert_request(
          user_id=user_id,
          tenant_id=tenant_id,
          requested_input=request.model_dump_json(),
          generated_response=None,
          request_date=datetime.now(timezone.utc),
          email_id=request.emailId,
          input_token_consumed=0,
          output_token_consumed=0,
          total_consumed=0,
          response_accepted_status=False,
          entity_id=request.entityId,
          entity_type=request.entityType,
          operation="email"
      )

      chat_completion = self.client.beta.chat.completions.parse(
          messages=[{"role": "system", "content": llm_prompt}],
          model="gpt-4o-mini",
          response_format=EmailOutputFormat,
      )

      input_token = chat_completion.usage.prompt_tokens
      output_token = chat_completion.usage.completion_tokens
      total_token_consumed = chat_completion.usage.total_tokens
      generated_email = chat_completion.choices[0].message.parsed

      logger.info(
          f"LLM Token Usage - input_tokens={input_token}, output_tokens={output_token}, total_tokens={total_token_consumed}, user_id={user_id}, tenant_id={tenant_id}"
      )

      # **Update token history**
      token_history_service.update_or_insert_token_history(
          user_id=user_id,
          tenant_id=tenant_id,
          token_limit=daily_token_limit,
          token_consumed=total_token_consumed,
          usage_date=date.today(),
      )

      # **Update request history with response details**
      request_history_service.update_request(
          request_id=inserted_data.id,
          generated_response={
            "subject": generated_email.subject,
            "content": generated_email.body,
          },
          input_token_consumed=input_token,
          output_token_consumed=output_token,
          total_consumed=total_token_consumed,
      )

      return generated_email, inserted_data.id

    except Exception as e:
      logger.error("Unexpected error generating email response", exc_info=e)

      # **Ensure the request is marked as failed if an error occurs**
      request_history_service.update_request(
          request_id=inserted_data.id,
          generated_response={"error": str(e)},
      )
      raise HTTPException(
          status_code=400,
          detail="An unexpected error occurred while generating the email response",
      )

  ##############################################################################################################
  def get_user_from_iam(self, user_id, token):

    base_url = os.getenv("IAM_BASE_PATH")

    if not base_url:
      logger.error(
          f"IAM_BASE_PATH environment variable is not set - environment=")
      raise ValueError("IAM_BASE_PATH environment variable is missing")

    full_url = f"{base_url}/v1/users/{user_id}"

    headers = {
      "Authorization": f"Bearer {token}"
    }

    try:
      response = requests.get(full_url, headers=headers)
      response.raise_for_status()

      user_data = response.json()
      logger.info(f"User data fetched from IAM: {user_data}")

      extracted_data = {
        "id": user_data.get("id"),
        "name": f"{user_data.get('firstName', '')} {user_data.get('lastName', '')}".strip(),
        "tenant_id": user_data.get("tenantId"),
        "email": user_data.get("email"),
      }

      logger.info(f"User data extracted from IAM: {extracted_data}")
      return extracted_data

    except requests.exceptions.RequestException as e:
      logger.error(
          f"Request error in get_user_from_iam - Error details: {str(e)}"
      )
      raise HTTPException(status_code=400,
                          detail="Something didn't work as expected.")

  ##############################################################################################################
  async def get_new_email_response(
      self,
      system_prompt: str,
      tenant_id,
      user_id,
      user_name,
      token: str,
      db_session,
      request: NewEmailRequestFormat,
  ) -> EmailOutputFormat:
    try:

      request_history_service = RequestHistoryService(db_session)
      token_history_service = TokenHistoryService(db_session)
      user_service = UserService(db_session)

      existing_user = user_service.get_user_by_id(user_id)
      logger.info(
          f"User data extracted from DB in create function - existing_user={existing_user}"
      )
      if not existing_user:
        logger.info(
            f"Fetching user data from IAM after DB lookup - existing_user={existing_user}"
        )
        iam_user = self.get_user_from_iam(user_id, token)
        logger.info(
            f"IAM user data retrieved successfully - iam_user={iam_user}"
        )
        if iam_user:
          # Insert user into local service
          existing_user = user_service.insert_user(
              id=iam_user["id"],
              name=iam_user["name"],
              email=iam_user["email"],
              tenant_id=iam_user["tenant_id"]
          )
          logger.info(
              f"Inserted new user from IAM - user_id={existing_user.id}"
          )

        else:
          logger.error(
              f"User not found in IAM - user_id={user_id}"
          )
          raise HTTPException(status_code=400,
                              detail="User not found")

      if token_history_service.is_token_limit_exceeded(
          user_id=user_id, tenant_id=tenant_id, usage_date=date.today()
      ):
        raise TokenLimitExceededException()

      schema_str = json.dumps(EmailOutputFormat.model_json_schema(), indent=2)

      email_recipient_name = f"email recipient name is {request.to[0].name}" if request.to else ""
      email_sender_name = f"email sender name is {existing_user.name}" if existing_user.name else ""

      messages = [
        {
          "role": "system",
          "content": (
            f"You are an expert in drafting professional emails and should generate the email response in JSON format. \n {email_recipient_name} \n {email_sender_name} \n"
            f"Follow these instructions carefully: {system_prompt}.\n\n"
            f"Ensure that the generated JSON strictly adheres to the following schema:\n{schema_str}"
          ),
        },
      ]

      chat_completion = self.client.beta.chat.completions.parse(
          messages=messages,
          model="gpt-4o-mini",
          response_format=EmailOutputFormat,
      )

      input_token = chat_completion.usage.prompt_tokens
      output_token = chat_completion.usage.completion_tokens
      total_token_consumed = chat_completion.usage.total_tokens

      generated_email = chat_completion.choices[0].message.parsed

      token_history_service.update_or_insert_token_history(
          user_id=user_id,
          tenant_id=tenant_id,
          token_limit=daily_token_limit,
          token_consumed=total_token_consumed,
          usage_date=date.today(),
      )

      inserted_data = request_history_service.insert_request(
          user_id=user_id,
          tenant_id=tenant_id,
          requested_input=request.model_dump_json(),
          generated_response={
            "subject": generated_email.subject,
            "content": generated_email.body,
          },
          request_date=datetime.now(timezone.utc),
          email_id=None,
          input_token_consumed=input_token,
          output_token_consumed=output_token,
          total_consumed=total_token_consumed,
          response_accepted_status=False,
          entity_id=request.entityId,
          entity_type=request.entityType,
          operation="email"
      )
      return generated_email, inserted_data.id

    except TokenLimitExceededException as token_exc:
      raise token_exc
    except Exception as e:
      logger.error(
          f"Unexpected error in get_new_email_response - Error: {str(e)}"
      )
      raise HTTPException(status_code=400,
                          detail="Something didn't work as expected.")

  ##############################################################################################################
  def update_response_status_accepted(self, request_id: int, status: bool,
      db_session):
    try:

      request_history_service = RequestHistoryService(db_session)
      request_history_service.update_response_status(
          request_id=request_id, status=status
      )
      logger.info(
          f"Email response status updated - request_id={request_id}, status={status}"
      )

    except Exception as e:
      logger.error(
          f"Error in update_response_status_accepted - Error: {str(e)}"
      )
      raise HTTPException(status_code=400,
                          detail="Something didn't work as expected. ")


  #####################################################################################################################

  async def get_rewritten_email_response(self,
      email_id,
      tenant_id,
      user_id,
      instruction,
      token,
      db_session)-> EmailOutputFormat:
        try:
            request_history_service = RequestHistoryService(db_session)
            token_history_service = TokenHistoryService(db_session)


            original_email = request_history_service.get_request_data_by_id(email_id)


            if not original_email:
                logger.error(f"No previous email found for userId={user_id}")
                raise HTTPException(status_code=404, detail="Previous email not found")


            if token_history_service.is_token_limit_exceeded(
                user_id=user_id, tenant_id=tenant_id, usage_date=date.today()
            ):
                raise TokenLimitExceededException()


            email_subject = original_email["generated_response"]["subject"]
            email_content = original_email["generated_response"]["content"]
            entity_id = original_email["entity_id"]
            entity_type = original_email["entity_type"]


            schema_str = json.dumps(EmailOutputFormat.model_json_schema(), indent=2)

            messages = [
                {
                    "role": "system",
                    "content": (
                        f"You are an expert in rewriting professional emails and should generate the response in JSON format.\n"
                        f"Here is the original email:\n\nSubject: {email_subject}\nContent: {email_content}\n\n"
                        f"Rewrite it based on this instruction: {instruction}.\n"
                        f"Ensure that the generated JSON strictly follows this schema:\n{schema_str}"
                    ),
                }
            ]

            chat_completion = self.client.beta.chat.completions.parse(
                messages=messages,
                model="gpt-4o-mini",
                response_format=EmailOutputFormat,
            )


            input_token = chat_completion.usage.prompt_tokens
            output_token = chat_completion.usage.completion_tokens
            total_token_consumed = chat_completion.usage.total_tokens
            rewritten_email = chat_completion.choices[0].message.parsed

            inserted_data = request_history_service.insert_request(
                user_id=user_id,
                tenant_id=tenant_id,
                requested_input=json.dumps({"instruction": instruction}),
                generated_response={
                    "subject": rewritten_email.subject,
                    "content": rewritten_email.body,
                },
                request_date=datetime.now(timezone.utc),
                email_id=original_email["id"],
                input_token_consumed=input_token,
                output_token_consumed=output_token,
                total_consumed=total_token_consumed,
                response_accepted_status=False,
                entity_id=entity_id,
                entity_type=entity_type,
                operation="email",
            )

            return rewritten_email, inserted_data.id

        except TokenLimitExceededException as token_exc:
            raise token_exc
        except Exception as e:
            logger.error(f"Unexpected error in get_rewritten_email_response - Error: {str(e)}")
            raise HTTPException(status_code=400, detail="Something didn't work as expected.")

