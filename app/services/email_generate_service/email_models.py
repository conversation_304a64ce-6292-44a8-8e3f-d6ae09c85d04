from typing import List, Optional, Dict, Any

from pydantic import BaseModel, EmailStr


class Recipient(BaseModel):
  id: Optional[str] = None
  name: Optional[str] = None
  email: EmailStr
  entity: Optional[str] = None
  label: Optional[str] = None


class NewEmailRequestFormat(BaseModel):
  subject: Optional[str] = None
  entityType: Optional[str] = None
  entityId: Optional[int] = None
  to: Optional[List[Recipient]] = []
  cc: Optional[List[Recipient]] = []
  bcc: Optional[List[Recipient]] = []
  content: str  # Mandatory field

class RewriteEmailRequestFormat(BaseModel):
  instruction: str


class ReplyEmailRequestFormat(BaseModel):
  subject: Optional[str] = None
  entityType: Optional[str] = None
  entityId: Optional[int] = None
  to: Optional[List[Recipient]] = []
  cc: Optional[List[Recipient]] = []
  bcc: Optional[List[Recipient]] = []
  content: str  # Mandatory field
  emailId: int  # Mandatory field


class EmailOutputFormat(BaseModel):
  subject: str
  body: str


class EmailResponseFormat(BaseModel):
  content: Dict[str, Any]
