"""
LangChain-based Note Analysis Service
"""
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timezone
from fastapi import HTTPException
from loguru import logger

from app.langchain_services.note_analysis_service.note_analysis_chains import NoteAnalysis<PERSON>hain
from app.note_analysis.models.note_analysis_models import NoteAnalysisResponse, ActionItem
from app.db.repository.request_history_repository import RequestH<PERSON>oryService
from app.db.repository.token_history_repository import TokenHistoryService
from app.exceptions import ExternalAIModelException
from app.langchain_core.monitoring import setup_langchain_monitoring


class LangChainNoteAnalysisService:
    """LangChain-based note analysis service"""
    
    def __init__(self, db_session=None):
        self.db_session = db_session
        self.note_analysis_chain = NoteAnalysisChain(db_session=db_session)
        self.request_history_service = RequestHistoryService(db_session) if db_session else None
        self.token_history_service = TokenHistoryService(db_session) if db_session else None
        
        # Setup monitoring
        setup_langchain_monitoring()
    
    async def analyze_note(
        self,
        note_content: str,
        user_id: str,
        tenant_id: str,
        note_id: str,
        user_mentions_mapping: Optional[Dict[str, Any]] = None
    ) -> NoteAnalysisResponse:
        """Analyze note content and extract action items using LangChain"""
        try:
            current_date_time_utc = datetime.now(timezone.utc).strftime(
                "%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
            
            logger.info("Note Analysis : Starting LangChain-based note analysis")
            logger.info(f"Note Analysis : Note content: {note_content}")
            
            # Prepare chain inputs
            chain_inputs = {
                "operation": "analyze_note",
                "note_content": note_content,
                "user_mentions_mapping": user_mentions_mapping or {}
            }
            
            # Execute chain
            result = await self.note_analysis_chain.execute(
                inputs=chain_inputs,
                user_id=user_id,
                tenant_id=tenant_id,
                operation="note_analysis",
                entity_id=note_id,
                entity_type="note"
            )
            
            action_items = result.get("action_items", [])
            
            logger.info("Note Analysis : Successfully analyzed note with LangChain")
            logger.info(f"Note Analysis : Extracted action items: {action_items}")
            
            # Calculate token usage
            input_tokens = result.get("input_tokens", 0)
            output_tokens = result.get("output_tokens", 0)
            total_tokens = result.get("total_tokens", 0)
            
            # Update token history
            if self.token_history_service:
                self.token_history_service.update_or_insert_token_history(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    token_limit=10000,  # Default limit, should come from config
                    token_consumed=total_tokens,
                    usage_date=date.today()
                )
            
            # Save request history
            if self.request_history_service:
                self.request_history_service.insert_request(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    requested_input={
                        "noteId": note_id,
                        "noteContent": note_content,
                        "userMentionsMapping": user_mentions_mapping
                    },
                    generated_response={
                        "actionItems": [item.dict() if hasattr(item, 'dict') else item for item in action_items]
                    },
                    request_date=datetime.now(timezone.utc),
                    email_id=None,
                    input_token_consumed=input_tokens,
                    output_token_consumed=output_tokens,
                    total_consumed=total_tokens,
                    response_accepted_status=True,
                    entity_id=note_id,
                    entity_type="note",
                    operation="note_analysis",
                    status="COMPLETED"
                )
            
            # Create response object
            response = NoteAnalysisResponse(
                noteId=note_id,
                actionItems=action_items,
                analysisDate=current_date_time_utc,
                status="completed"
            )
            
            logger.info("Note Analysis : Successfully completed LangChain-based analysis")
            return response
            
        except Exception as e:
            logger.error(f"Note Analysis : LangChain analysis failed: {str(e)}", exc_info=True)
            raise ExternalAIModelException(message=str(e))
    
    async def extract_action_items_only(
        self,
        note_content: str,
        user_mentions_mapping: Optional[Dict[str, Any]] = None
    ) -> List[ActionItem]:
        """Extract only action items without full analysis workflow"""
        try:
            logger.info("Note Analysis : Extracting action items only")
            
            # Prepare chain inputs
            chain_inputs = {
                "operation": "analyze_note",
                "note_content": note_content,
                "user_mentions_mapping": user_mentions_mapping or {}
            }
            
            # Execute chain without database tracking
            result = await self.note_analysis_chain._execute_chain(chain_inputs)
            action_items = result.get("action_items", [])
            
            logger.info(f"Note Analysis : Extracted {len(action_items)} action items")
            return action_items
            
        except Exception as e:
            logger.error(f"Note Analysis : Action item extraction failed: {str(e)}", exc_info=True)
            raise ExternalAIModelException(message=str(e))
    
    def _process_user_mentions(
        self, 
        note_content: str, 
        user_mentions_mapping: Dict[str, Any]
    ) -> str:
        """Process user mentions in note content"""
        if not user_mentions_mapping:
            return note_content
        
        processed_content = note_content
        for placeholder, user_info in user_mentions_mapping.items():
            if isinstance(user_info, dict):
                user_name = user_info.get("name", placeholder)
                processed_content = processed_content.replace(placeholder, user_name)
            else:
                processed_content = processed_content.replace(placeholder, str(user_info))
        
        return processed_content
