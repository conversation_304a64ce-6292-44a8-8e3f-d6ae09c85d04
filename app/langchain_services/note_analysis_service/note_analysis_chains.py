"""
LangChain-based Note Analysis Chains
"""
from typing import Dict, Any, List, Optional
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field
import json
from loguru import logger

from app.langchain_core.base_chain import BaseAIChain
from app.langchain_core.monitoring import get_callback_handler
from app.note_analysis.models.note_analysis_models import ActionItem


class NoteAnalysisOutput(BaseModel):
    """Structured output for note analysis"""
    actionItems: List[ActionItem] = Field(description="List of action items extracted from the note")


class NoteAnalysisChain(BaseAIChain):
    """LangChain implementation for note analysis and action item extraction"""
    
    def _initialize_chain(self, **kwargs):
        """Initialize note analysis chain components"""
        self.output_parser = PydanticOutputParser(pydantic_object=NoteAnalysisOutput)
        
        # Note analysis prompt template
        self.note_analysis_prompt_template = PromptTemplate(
            template="""You are a helpful assistant that extracts action items from notes.

<identity>
You are an expert at analyzing business notes and extracting actionable tasks and follow-ups.
</identity>

<instructions>
Analyze the provided note content and extract all action items, tasks, and follow-ups mentioned.
For each action item, determine:
1. A clear, concise description of the task
2. The priority level (HIGH, MEDIUM, LOW)
3. Who is responsible (if mentioned)
4. When it should be completed (if mentioned)
5. Any additional context or details

Focus on:
* Explicit action items ("need to", "should", "must", "will")
* Follow-up tasks ("follow up with", "check on", "review")
* Deadlines and time-sensitive items
* Commitments made during meetings or conversations
* Next steps mentioned
</instructions>

<user_mentions>
{user_mentions_info}
</user_mentions>

<note_content>
{note_content}
</note_content>

<guidelines>
* Extract only clear, actionable items
* Avoid vague or unclear tasks
* If no action items are found, return an empty list
* Use the user mentions mapping to identify responsible parties
* Prioritize based on urgency and importance mentioned in the note
* Include context when it helps clarify the action item
</guidelines>

{format_instructions}

Example response:
{{
  "actionItems": [
    {{
      "description": "Follow up with John about the project timeline",
      "priority": "HIGH",
      "assignee": "John Smith",
      "dueDate": "2024-01-15",
      "context": "Discussed during weekly meeting"
    }},
    {{
      "description": "Review quarterly budget proposal",
      "priority": "MEDIUM",
      "assignee": "Finance Team",
      "dueDate": null,
      "context": "Needs approval before month end"
    }}
  ]
}}""",
            input_variables=["note_content", "user_mentions_info"],
            partial_variables={"format_instructions": self.output_parser.get_format_instructions()}
        )
        
        # Create chain
        self.note_analysis_chain = self.note_analysis_prompt_template | self.llm | self.output_parser
    
    async def _execute_chain(self, inputs: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Execute the note analysis chain"""
        operation = inputs.get("operation", "analyze_note")
        callback_handler = get_callback_handler(f"note_analysis_{operation}")
        
        try:
            if operation == "analyze_note":
                result = await self._analyze_note(inputs, callback_handler)
            else:
                raise ValueError(f"Unknown note analysis operation: {operation}")
            
            # Extract token usage from callback handler
            tokens = callback_handler.tokens_used
            result.update({
                "input_tokens": tokens["input"],
                "output_tokens": tokens["output"],
                "total_tokens": tokens["total"]
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Note analysis chain execution failed: {str(e)}")
            raise
    
    async def _analyze_note(self, inputs: Dict[str, Any], callback_handler) -> Dict[str, Any]:
        """Analyze note content and extract action items"""
        note_content = inputs.get("note_content", "")
        user_mentions_mapping = inputs.get("user_mentions_mapping", {})
        
        # Format user mentions information
        user_mentions_info = self._format_user_mentions(user_mentions_mapping)
        
        chain_inputs = {
            "note_content": note_content,
            "user_mentions_info": user_mentions_info
        }
        
        result = await self.note_analysis_chain.ainvoke(
            chain_inputs,
            config={"callbacks": [callback_handler]}
        )
        
        return {
            "analysis_result": result,
            "action_items": result.actionItems,
            "operation": "analyze_note"
        }
    
    def _format_user_mentions(self, user_mentions_mapping: Dict[str, Any]) -> str:
        """Format user mentions mapping for the prompt"""
        if not user_mentions_mapping:
            return "No user mentions provided."
        
        formatted_mentions = []
        for placeholder, user_info in user_mentions_mapping.items():
            if isinstance(user_info, dict):
                name = user_info.get("name", "Unknown")
                user_id = user_info.get("id", "Unknown")
                formatted_mentions.append(f"- {placeholder}: {name} (ID: {user_id})")
            else:
                formatted_mentions.append(f"- {placeholder}: {user_info}")
        
        return "\n".join(formatted_mentions) if formatted_mentions else "No user mentions provided."
