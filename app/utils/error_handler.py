from typing import Optional
from fastapi import HTTPException
from loguru import logger
from sqlalchemy.exc import SQLAlchemyError

from app.exceptions import AuthenticationError, TokenLimitExceededException, RateLimitExceededException, \
    ResourceNotFoundException, NamespaceNotFoundException, FiltersNotFoundException, FileSizeLimitExceededException, \
    ExternalAIModelException, RabbitMQPublishException, TaskFormFieldExtractionError, TaskCreationError, \
    CallAnalysisInProgressException, InsufficientPermissionsException, AddonNotPresentException


class ErrorHandler:
    @staticmethod
    def handle_error(exception: Exception, message: Optional[str] = None):
        logger.error(str(exception), exc_info=True)
        # AuthenticationError
        if isinstance(exception, AuthenticationError):
            raise HTTPException(status_code=401, detail=exception.to_dict())
        # Token Limit Exceeded
        elif isinstance(exception, TokenLimitExceededException):
            raise HTTPException(status_code=429, detail=exception.to_dict())
        # Rate Limit Exceeded
        elif isinstance(exception, RateLimitExceededException):
            raise HTTPException(status_code=429, detail=exception.to_dict())
        # Namespace Not Found
        elif isinstance(exception, NamespaceNotFoundException):
            raise HTTPException(status_code=404, detail=exception.to_dict())
        # Filters Not Found
        elif isinstance(exception, FiltersNotFoundException):
            raise HTTPException(status_code=404, detail=exception.to_dict())
        # Resource Not Found
        elif isinstance(exception, ResourceNotFoundException):
            raise HTTPException(status_code=404, detail=exception.to_dict())
        # File Size Limit Exceeded
        elif isinstance(exception,FileSizeLimitExceededException):
            raise HTTPException(status_code=413, detail=exception.to_dict())
        # External AI
        elif isinstance(exception, ExternalAIModelException):
            raise HTTPException(status_code=502, detail=exception.to_dict())
        # Rabbit MQ
        elif isinstance(exception,RabbitMQPublishException):
            raise HTTPException(status_code=502, detail=exception.to_dict())
        # Task Form Field extraction error
        elif isinstance(exception, TaskFormFieldExtractionError):
            raise HTTPException(status_code=422, detail=exception.to_dict())
        # Task creation error
        elif isinstance(exception,TaskCreationError):
            raise HTTPException(status_code=422, detail=exception.to_dict())
        # Call Analysis
        elif isinstance(exception, CallAnalysisInProgressException):
            raise HTTPException(status_code=409, detail=exception.to_dict())
        # SQL Alchemy Error
        elif isinstance(exception, SQLAlchemyError):
            raise HTTPException(status_code=500, detail="Something went wrong while processing your request.")
        # Insufficient Permissions
        elif isinstance(exception, InsufficientPermissionsException):
            raise HTTPException(status_code=403, detail=exception.to_dict())
        # Addon Not Present
        elif isinstance(exception, AddonNotPresentException):
            raise HTTPException(status_code=403, detail=exception.to_dict())
        # Generic Exception
        else:
            raise HTTPException(status_code=500, detail="Something went wrong while processing your request.")