import os
from typing import Op<PERSON>, Any

import requests
from fastapi import HTTPException

from app.db.repository.users_repository import UserService
from app.rabbitmq.config import logger
from app.utils.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from app.usage.usage_model import UsageResponse, AddonType


class AppUtils:
    @staticmethod
    async def get_user_from_iam(user_id: str, token: str):
        try:
            base_path = os.getenv("IAM_BASE_PATH")
            if base_path is None:
                logger.error(f"IAM_BASE_PATH is not set")
            else:
                response = requests.get(
                    f"{base_path}/v1/users/{user_id}",
                    headers={
                        "Authorization": f"Bearer {token}"
                    }
                )
                response.raise_for_status()
                user_data = response.json()
                data =  {
                    "id": user_data.get("id"),
                    "tenant_id": user_data.get("tenantId"),
                    "name": f"{user_data.get('firstName')} {user_data.get('lastName')}".strip(),
                    "email": user_data.get("email"),
                }
                return data
        except Exception as e:
            ErrorHandler.handle_error(e)


    @staticmethod
    async def get_user(
            user_id: str,
            token: str,
            user_service: UserService,
    ) -> Optional[Any]:
        # find if user exists
        user = user_service.get_user_by_id(user_id)

        if user is None:
            # get user from iam
            user = await AppUtils.get_user_from_iam(user_id, token)
            if user is None:
                raise HTTPException(
                    status_code=404,
                    detail="User not found"
                )
            else:
                # save into user service
                user_service.insert_user(
                    id=user.get("id"),
                    tenant_id=user.get("tenant_id"),
                    name=user.get("name"),
                    email=user.get("email"),
                )
        return user

    @staticmethod
    async def get_usage(token:str) -> Optional[UsageResponse]:
        try:
            iam_base_path = os.getenv("IAM_BASE_PATH")
            if iam_base_path is None:
                logger.error(f"IAM_BASE_PATH is not set")
            else:
                response = requests.get(
                    f"{iam_base_path}/v1/tenants/usage",
                    headers={
                        "Authorization": f"Bearer {token}"
                    }
                )
                response.raise_for_status()
                return UsageResponse(**response.json())
        except Exception as e:
            ErrorHandler.handle_error(e)


    @staticmethod
    async def has_addon(addon : AddonType, token : str) -> Optional[bool]:
        try:
            usage = await AppUtils.get_usage(token)
            for addon_info in usage.addons:
                if addon_info.id == addon.value:
                    return True
            return False
        except Exception as e:
            ErrorHandler.handle_error(e)

