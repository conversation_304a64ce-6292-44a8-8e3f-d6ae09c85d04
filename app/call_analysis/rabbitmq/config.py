import aio_pika
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get RabbitMQ URL from .env
RABBITMQ_URL = os.getenv("RABBITMQ_URL")

if not RABBITMQ_URL:
    raise ValueError("RABBITMQ_URL is not set in the environment")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def setup_rabbitmq():
    try:
        connection = await aio_pika.connect_robust(RABBITMQ_URL)
        channel = await connection.channel()

        queues = {}

        # Use the workflow exchange and queue
        ex_workflow = await channel.declare_exchange("ex.workflow", aio_pika.ExchangeType.TOPIC, durable=True)
        q_workflow_calllog = await channel.declare_queue("q.workflow.generate.callLog.summary.ai", durable=True)
        await q_workflow_calllog.bind(ex_workflow, routing_key="workflow.generate.callLog.summary")
        queues["workflow_calllog"] = q_workflow_calllog

        logger.info("Call Analysis RabbitMQ setup completed!")

        return connection, channel, queues

    except Exception as e:
        logger.error(f"Error setting up RabbitMQ for call analysis: {e}")
        raise