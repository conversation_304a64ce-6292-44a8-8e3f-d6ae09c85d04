import os
import json
import aio_pika
from datetime import datetime

from app.db.repository.request_history_repository import RequestHistoryService
from app.note_analysis.services.build_jwt_token_note import \
    build_jwt_token_for_analysis
from app.services.logger_config import logger
from fastapi import Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from sqlalchemy.pool import NullPool

from app.call_analysis.services.get_call_recording_url import get_call_recording_url
from app.call_analysis.services.download_audio_file import download_audio_file
from app.call_analysis.services.analyze_audio import analyze_audio
from app.call_analysis.services.get_task_form_fields import get_task_form_fields
from app.call_analysis.services.create_task import create_task
from app.exceptions import (
    FileSizeLimitExceededException,
    TaskCreationError,
)
from app.call_analysis.rabbitmq.publisher import publish_call_analysis_response
from app.call_analysis.services.build_jwt_token import build_jwt_token
from app.exceptions import Authentication<PERSON>rror, ExternalAIModelException, RabbitMQPublishException, RateLimitExceededException, TaskFormFieldExtractionError
from app.db.repository.users_repository import UserService
from app.api.smart_list.v1.smart_list_api import get_user_from_iam
from app.call_analysis.services.is_call_analysis_feature_enabled import is_call_analysis_feature_enabled
# from app.rate_limit.rate_limiter import rate_limiter as openai_rate_limiter, get_redis as openai_get_redis

DATABASE_URL = os.getenv("DATABASE_URL")
engine = create_engine(DATABASE_URL, poolclass=NullPool)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()

async def consume_calllog(queue):
    db_session = next(get_db())
    request_history_service = RequestHistoryService(db_session)
    async with queue.iterator() as queue_iter:
        async for message in queue_iter:
            logger.info(f"Received message from queue for call log: {message}")
            reply_to_exchange = message.headers.get("replyToExchange")
            reply_to_event = message.headers.get("replyToEvent")
            logger.info(f"Reply to exchange: {reply_to_exchange}, Reply to event: {reply_to_event}")
            audio_file_path = None
            async with message.process():
                try:
                    try:
                        payload = json.loads(message.body)
                    except json.JSONDecodeError as json_err:
                        logger.error(f"Invalid JSON format in message: {str(json_err)}")
                        logger.debug(f"Message body: {message.body}")
                        continue

                    logger.info(f"Call Analysis : Received message: {payload}")

                    entity = payload.get("entity", {})
                    metadata = payload.get("metadata", {})

                    logger.info(f"Call Analysis : Message metadata: {metadata}")

                    call_id = entity.get("callLogId")
                    tenant_id = metadata.get("tenantId")
                    event_id = metadata.get("eventId")
                    user_id = metadata.get("userId")
                    client_prompt = entity.get("prompt")

                    # checking rate limit for openai
                    # redis = await openai_get_redis()
                    # logger.info(f"Call Analysis : Checking open_ai rate limit for tenant {tenant_id}")
                    # await openai_rate_limiter(tenant_id, redis)

                    # Generate JWT token first
                    token, tenantUserId = build_jwt_token_for_analysis(user_id, tenant_id)
                    logger.info(f"Generated JWT token for user_id={user_id}, tenant_id={tenant_id} token={token}")

                    # check if call analysis feature is enabled
                    logger.info(f"Call Analysis : Checking if call analysis feature is enabled...")
                    call_analysis_feature_enabled = is_call_analysis_feature_enabled(token)
                    logger.info(f"Call Analysis : Call analysis feature enable status for tenant {tenant_id} & user {user_id} : {call_analysis_feature_enabled}")

                    if call_analysis_feature_enabled:
                        logger.info(
                            f"Processing callLogId={call_id}, tenantId={tenant_id}, userId={user_id}"
                        )

                        # We already generated the JWT token above, so we don't need to do it again
                        logger.info(f"Generated JWT token for userId={user_id} and token={token}")

                        # check if user exists in db
                        user_service = UserService(db_session)
                        existing_user = user_service.get_user_by_id(user_id)
                        logger.info(f"Call Analysis : User data extracted from DB in create function - existing_user={existing_user}")

                        # if user does not exist in db, fetch user data from IAM
                        if not existing_user:
                            logger.info(f"Call Analysis : Fetching user data from IAM after DB lookup - existing_user={existing_user}")
                            iam_user = get_user_from_iam(user_id, token)
                            logger.info(f"Call Analysis : IAM user data retrieved successfully - iam_user={iam_user}")
                            if iam_user:
                                # Insert user into local service
                                existing_user = user_service.insert_user(
                                    id=iam_user["id"],
                                    name=iam_user["name"],
                                    email=iam_user["email"],
                                    tenant_id=iam_user["tenant_id"]
                                )
                                logger.info(f"Call Analysis : Inserted new user from IAM - user_id={existing_user.id}")

                            else:
                                logger.error(f"Call Analysis : User not found in IAM - user_id={user_id}")
                                raise HTTPException(status_code=400, detail="User not found")

                        # Get the call recording URL
                        audio_url, related_to = get_call_recording_url(call_id, token)
                        logger.info(f"Got the audio URL")

                        # Download the audio file
                        logger.info(f"Downloading audio file...")
                        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                        audio_file_path = f"/tmp/{call_id}_{timestamp}.mp3"
                        download_audio_file(audio_url, audio_file_path)
                        logger.info(f"Audio file downloaded")

                        # Check the file size
                        logger.info(f"Checking audio file size...")
                        file_size_mb = os.path.getsize(audio_file_path) / (1024 * 1024)
                        file_size_limit_mb = int(os.getenv("AUDIO_FILE_SIZE_LIMIT_MB"))

                        if file_size_mb > file_size_limit_mb:
                            logger.error(f"Audio file size exceeds the specified limit for callId={call_id}")
                            raise FileSizeLimitExceededException(message=f"Audio file size exceeds the specified limit of {file_size_limit_mb} MB")
                        else:
                            logger.info(f"Audio file size {file_size_mb:.2f} MB is within the specified limit")

                        # Transcribe the audio file
                        logger.info(f"Transcribing audio file...")
                        response, action_item_details, inserted_data = analyze_audio(file_path=audio_file_path,
                                                db_session=db_session,
                                                user_id=user_id,
                                                tenant_id=tenant_id,
                                                call_id=call_id,
                                                client_prompt=client_prompt)
                        logger.info(f"Response after call analysis: {response.json()}")
                        logger.info(f"Action item details: {action_item_details}")
                        logger.info(f"Successfully generated call analysis for audio file")

                        # Publish the response to RabbitMQ
                        logger.info(f"Publishing call analysis response to RabbitMQ...")
                        await publish_call_analysis_response(response, metadata, user_id)
                        logger.info(f"Successfully Published call analysis response to RabbitMQ")

                        # get task form fields usin kylas api
                        logger.info(f"Getting task form fields...")
                        task_form_fields_response = get_task_form_fields(token)
                        logger.info(f"Got task form fields successfully {task_form_fields_response}")

                        # Create tasks for each action item
                        logger.info(f"Creating tasks for action items ",action_item_details)
                        for action_item_detail in action_item_details:
                            logger.info(f"Action item detail: {action_item_detail}")
                            try:
                                task_name = action_item_detail.get("actionItem")
                                task_priority = action_item_detail.get("priority", "MEDIUM")
                                task_due_date = action_item_detail.get("dueDate")

                                logger.info(f"Creating task for action item: {task_name}, priority: {task_priority}, due date: {task_due_date}")

                                await create_task(
                                    token=token,
                                    user_id=tenantUserId,
                                    tenant_id=tenant_id,
                                    assigned_to=user_id,
                                    task_form_fields_response=task_form_fields_response,
                                    action_item=task_name,
                                    priority=task_priority,
                                    due_date=task_due_date,
                                    related_to=related_to
                                )
                                logger.info(f"Task creation event published successfully for action item: {task_name}")
                            except (TaskCreationError, RabbitMQPublishException) as e:
                                logger.info(f"Task creation error for action item '{task_name}': {str(e)}")

                        await publish_workflow_response(
                            status="SUCCESS",
                            status_code=200,
                            channel=queue.channel,
                            reply_to_exchange=reply_to_exchange,
                            reply_to_event=reply_to_event,
                            entity_id=call_id,
                            event_id=event_id
                        )
                        logger.info(
                            f"Published workflow response for callId={call_id}")

                        request_history_service.update_request(
                            request_id=inserted_data.id, status="COMPLETED"
                        )
                        logger.info(f"Request history updated successfully for callId={call_id}")

                    else:
                        logger.info(f"Call analysis feature is not enabled, skipping call analysis")

                except AuthenticationError as auth_exc:
                    logger.error("Authentication Error during call analysis", extra={"error": str(auth_exc)})
                    await publish_workflow_response(
                        status="FAILED",
                        status_code=400,
                        entity_id=call_id,
                        channel=queue.channel,
                        reply_to_exchange=reply_to_exchange,
                        reply_to_event=reply_to_event,
                        event_id=event_id,
                        error_code=auth_exc.errorCode,
                        error_message="Authentication Error during call analysis"
                    )
                    logger.error(f"Published workflow response for callId={call_id} with Authentication error")
                    raise HTTPException(status_code=401, detail="Authentication Error during call analysis")

                except FileSizeLimitExceededException as size_exc:
                    logger.error(f"File size limit exceeded during call analysis: {str(size_exc)}")
                    await publish_workflow_response(
                        status="FAILED",
                        status_code=400,
                        entity_id=call_id,
                        channel=queue.channel,
                        reply_to_exchange=reply_to_exchange,
                        reply_to_event=reply_to_event,
                        event_id=event_id,
                        error_code=size_exc.error_code,
                        error_message=size_exc.message
                    )
                    logger.error(f"Published workflow response for callId={call_id} with FileSizeLimitExceededException error")
                    return JSONResponse(
                        status_code=400,
                        content={
                            "error_code": size_exc.error_code,
                            "detail": size_exc.message,
                        },
                    )

                except ExternalAIModelException as ai_exc:
                    logger.error(f"External AI model error during call analysis: {str(ai_exc)}")
                    await publish_workflow_response(
                        status="FAILED",
                        status_code=400,
                        entity_id=call_id,
                        channel=queue.channel,
                        reply_to_exchange=reply_to_exchange,
                        reply_to_event=reply_to_event,
                        event_id=event_id,
                        error_code=ai_exc.error_code,
                        error_message=ai_exc.message
                    )
                    logger.error(f"Published workflow response for callId={call_id} with ExternalAIModelException error")
                    return JSONResponse(
                        status_code=400,
                        content={
                            "error_code": ai_exc.error_code,
                            "detail": ai_exc.message,
                        },
                    )

                except RabbitMQPublishException as publish_exc:
                    logger.error(f"RabbitMQ publish error during call analysis: {str(publish_exc)}")
                    await publish_workflow_response(
                        status="FAILED",
                        status_code=400,
                        entity_id=call_id,
                        channel=queue.channel,
                        reply_to_exchange=reply_to_exchange,
                        reply_to_event=reply_to_event,
                        event_id=event_id,
                        error_code=publish_exc.error_code,
                        error_message=publish_exc.message
                    )
                    logger.error(f"Published workflow response for callId={call_id} with RabbitMQPublishException error")
                    return JSONResponse(
                        status_code=400,
                        content={
                            "error_code": publish_exc.error_code,
                            "detail": publish_exc.message,
                        },
                    )

                except RateLimitExceededException as rate_exc:
                    logger.error(f"Rate limit exceeded during call analysis: {str(rate_exc)}")
                    await publish_workflow_response(
                        status="FAILED",
                        status_code=429,
                        entity_id=call_id,
                        channel=queue.channel,
                        reply_to_exchange=reply_to_exchange,
                        reply_to_event=reply_to_event,
                        event_id=event_id,
                        error_code=rate_exc.error_code,
                        error_message=rate_exc.message
                    )
                    logger.error(f"Published workflow response for callId={call_id} with RateLimitExceededException error")
                    return JSONResponse(
                        status_code=429,
                        content={
                            "error_code": rate_exc.error_code,
                            "detail": rate_exc.message,
                        },
                    )

                except TaskFormFieldExtractionError as e:
                    logger.error(f"Task form field extraction error: {str(e)}")
                    await publish_workflow_response(
                        status="FAILED",
                        status_code=400,
                        entity_id=call_id,
                        channel=queue.channel,
                        reply_to_exchange=reply_to_exchange,
                        reply_to_event=reply_to_event,
                        event_id=event_id,
                        error_code=e.error_code,
                        error_message=e.message
                    )
                    logger.error(f"Published workflow response for callId={call_id} with TaskFormFieldExtractionError error")
                    return JSONResponse(
                        status_code=400,
                        content=e.to_dict(),
                    )

                except Exception as e:
                    error_detail = getattr(e, "detail", "Something went wrong")
                    logger.error(f"Unexpected error during call analysis: {str(e)}")
                    await publish_workflow_response(
                        status="FAILED",
                        status_code=500,
                        entity_id=call_id,
                        channel=queue.channel,
                        reply_to_exchange=reply_to_exchange,
                        reply_to_event=reply_to_event,
                        event_id=event_id,
                        error_code="000000",
                        error_message="Internal server error"
                    )
                    logger.error(f"Published workflow response for callId={call_id} with unexpected error")
                    raise HTTPException(status_code=400, detail=error_detail)

                finally:
                    if os.path.exists(audio_file_path):
                        os.remove(audio_file_path)
                        logger.info(f"Deleted temporary audio file: {audio_file_path}")



async def publish_workflow_response(
    status,
    status_code,
    entity_id,
    channel,
    reply_to_exchange,
    reply_to_event,
    event_id=80,
    error_code=None,
    error_message=None,
    execution_details=None,
):
    """
    Publishes a workflow response message to the given exchange and routing key.
    """
    response_obj = {
        "eventId": event_id,
        "status": status,
        "statusCode": status_code,
        "statusUpdatedAt": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ"),
        "errorCode": error_code,
        "errorMessage": error_message,
        "entityId": entity_id,
        "executionDetails": execution_details or {},
    }

    exchange = await channel.declare_exchange(reply_to_exchange, aio_pika.ExchangeType.TOPIC, durable=True)
    message = aio_pika.Message(
        body=json.dumps(response_obj).encode(),
        content_type="application/json",
        delivery_mode=aio_pika.DeliveryMode.PERSISTENT
    )
    await exchange.publish(message, routing_key=reply_to_event)