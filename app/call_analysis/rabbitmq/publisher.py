import json
import os
import aio_pika
from loguru import logger
from app.call_analysis.models.call_analysis_models import CallAnalysisResponse
from app.exceptions import RabbitMQPublishException

# Get RabbitMQ URL and exchange from environment variables
RABBITMQ_URL = os.getenv("RABBITMQ_URL")
AI_EXCHANGE = os.getenv("AI_EXCHANGE")

async def publish_call_analysis_response(data: CallAnalysisResponse, metadata: dict, user_id: int):
    """
    Publishes the CallAnalysisResponse to the RabbitMQ exchange asynchronously.
    Args:
        call_id (int): The ID of the call.
        summary (str): The summary of the call analysis.
        sentiment (str): The sentiment of the call analysis.
    """
    try:
        logger.info(f"Call Analysis : Publishing call analysis response for call = {data}")

        if not RABBITMQ_URL:
          logger.error("RABBITMQ_URL variable is not set")
          raise ValueError("RABBITMQ_URL variable is missing")

        if not AI_EXCHANGE:
          logger.error("AI_EXCHANGE variable is not set")
          raise ValueError("AI_EXCHANGE variable is missing")

        # Connect to RabbitMQ
        connection = await aio_pika.connect_robust(RABBITMQ_URL)
        async with connection:
            # Create a channel
            channel = await connection.channel()

            # Declare the exchange
            exchange = await channel.declare_exchange(AI_EXCHANGE, aio_pika.ExchangeType.TOPIC, durable=True)

            # Create the message payload
            message = {
                "entity": {
                    "callId": data.callId,
                    "summary": data.summary,
                    "transcript": data.transcript,
                    "sentiment": data.sentiment,
                    "customerEmotion": data.customerEmotion
                },
                "userId": user_id,
                "metadata": metadata
            }

            # Publish the message
            await exchange.publish(
                aio_pika.Message(
                    body=json.dumps(message).encode(),
                    content_type="application/json",
                    delivery_mode=aio_pika.DeliveryMode.PERSISTENT
                ),
                routing_key="ai.call_analysis.generated"
            )

            logger.info(f"Published call-analysis message to exchange '{AI_EXCHANGE}' with routing key 'ai.call_analysis.generated' with message: {message}")

    except Exception as e:
        logger.error(f"Failed to publish message to RabbitMQ: {str(e)}")
        raise RabbitMQPublishException(message="Failed to publish message to RabbitMQ.")
