import google.generativeai as genai
import os
import re
import json
from loguru import logger
from datetime import datetime, timezone

from app.db.repository.request_history_repository import RequestHistoryService
from app.db.repository.token_history_repository import TokenHistoryService
from app.call_analysis.models.call_analysis_models import CallAnalysisResponse
from app.exceptions import ExternalAIModelException
from bs4 import BeautifulSoup
from app.rate_limit.gemini_rate_limiter import GEMINI_MAX_GLOBAL_TPM
from app.call_analysis.services.transcribe_audio import transcribe_audio
from app.call_analysis.services.summarize_text import summarize_text

# Configure the API key
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=GEMINI_API_KEY)

SENTIMENT_MAP = {
    "positive": "Positive",
    "negative": "Negative",
    "neutral": "Neutral"
}

CUSTOMER_EMOTION_MAP = {
    "interested": "Interested",
    "not interested": "Not Interested",
    "ready to buy": "Ready to Buy",
    "frustrated": "Frustrated"
}

def extract_json_field(pattern: str, text: str, default=None, flags=0):
  match = re.search(pattern, text, flags)
  if match:
    try:
      return json.loads(match.group(1))
    except json.JSONDecodeError:
      return match.group(1).strip()
  return default

def extract_action_items(text: str):
  action_items = []
  pattern = r'"actionItems":\s*(\[[^\]]*\])'
  match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
  if match:
    try:
      action_items = json.loads(match.group(1))
    except json.JSONDecodeError:
      logger.warning(
          "Failed to parse actionItems JSON, falling back to regex extraction")
      flat_array = re.findall(r'"([^"]+)"', match.group(1))
      i = 0
      while i + 5 < len(flat_array):
        if (flat_array[i] == "actionItem" and
            flat_array[i + 2] == "priority" and
            flat_array[i + 4] == "dueDate"):
          action_items.append({
            "actionItem": flat_array[i + 1],
            "priority": flat_array[i + 3],
            "dueDate": flat_array[i + 5]
          })
          i += 6
        else:
          logger.warning(
              f"Unexpected action item structure at index {i}: {flat_array[i:i + 6]}")
          i += 1
  else:
    logger.info("No action items found in the response")
  return action_items

def normalize_sentiment(value):
    if not value:
        return "Neutral"
    key = value.strip().lower()
    return SENTIMENT_MAP.get(key, "Neutral")

def normalize_customer_emotion(emotions):
    if not emotions:
        return ["Not Interested"]
    normalized = []
    for e in emotions:
        key = e.strip().lower()
        mapped = CUSTOMER_EMOTION_MAP.get(key)
        if mapped:
            normalized.append(mapped)
    return normalized or ["Not Interested"]

def analyze_audio(file_path: str, db_session, user_id, tenant_id, call_id,
    client_prompt) -> CallAnalysisResponse:
  """
  Transcribes the audio file using Google's Gemini API, then analyzes the transcript.
  Args:
      file_path (str): The path to the audio file.
  Returns:
      CallAnalysisResponse: The analysis response object.
  """
  request_history_service = RequestHistoryService(db_session)

  current_date_time_utc = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"

  try:
    logger.info("Call Analysis : Inside analyze_audio function")
    # Step 1: Transcribe audio
    transcript_response = transcribe_audio(file_path,client_prompt)
    if not transcript_response:
      raise ExternalAIModelException(message="Transcription failed")

    # Step 2: Analyze transcript
    analysis_response = summarize_text(transcript_response.text,client_prompt,current_date_time_utc)
    if not analysis_response:
      raise ExternalAIModelException(message="Analysis failed")

    # Extract fields from response text
    html_match = re.search(r'```html\n([\s\S]+?)\n```', transcript_response.text)
    if html_match:
        transcript_text = html_match.group(1)
    else:
        transcript_text = transcript_response.text

    analysis_text = analysis_response.text

    # If analysis_text is a code block (e.g., ```json ... ```), extract the JSON inside
    codeblock_match = re.search(r'```(?:json)?\n([\s\S]+?)\n```', analysis_text)
    analysis_json = None
    if codeblock_match:
        try:
            analysis_json = json.loads(codeblock_match.group(1))
        except Exception as e:
            logger.warning(f"Call Analysis : Failed to parse JSON from code block: {e}")
    else:
        try:
            analysis_json = json.loads(analysis_text)
        except Exception:
            analysis_json = None

    if analysis_json:
        summary = analysis_json.get("summary", "No summary available")
        sentiment = analysis_json.get("sentiment", "Neutral")
        customer_emotion = analysis_json.get("customerEmotion", ["Not Interested"])
        action_items = analysis_json.get("actionItems", [])
    else:
        summary = extract_json_field(r'"summary":\\s*"([^"]*)"', analysis_text, default="No summary available")
        sentiment = extract_json_field(r'"sentiment":\\s*"(Positive|Negative|Neutral|positive|negative|neutral)"', analysis_text, default="neutral")
        customer_emotion = extract_json_field(r'"customerEmotion":\\s*(\[[^\]]*\])', analysis_text, default='["not interested"]')
        if isinstance(customer_emotion, str):
            try:
                customer_emotion = json.loads(customer_emotion)
            except json.JSONDecodeError:
                customer_emotion = ["Not Interested"]
        action_items = extract_action_items(analysis_text)

    normalized_sentiment = normalize_sentiment(sentiment)
    normalized_customer_emotion = normalize_customer_emotion(customer_emotion)
    logger.info(f"Call Analysis : summary : {summary} , sentiment : {normalized_sentiment} , customer emotions : {normalized_customer_emotion} , action items : {action_items}")

    token_history_service = TokenHistoryService(db_session)
    # Sum tokens from both Gemini calls
    transcript_usage_metadata = transcript_response.usage_metadata
    analysis_usage_metadata = analysis_response.usage_metadata
    input_tokens = (getattr(transcript_usage_metadata, 'prompt_token_count', 0) or 0) + (getattr(analysis_usage_metadata, 'prompt_token_count', 0) or 0)
    output_tokens = (getattr(transcript_usage_metadata, 'candidates_token_count', 0) or 0) + (getattr(analysis_usage_metadata, 'candidates_token_count', 0) or 0)
    total_tokens = (getattr(transcript_usage_metadata, 'total_token_count', 0) or 0) + (getattr(analysis_usage_metadata, 'total_token_count', 0) or 0)
    logger.info(
        f"Call Analysis : Input Tokens: {input_tokens}, Output Tokens: {output_tokens}, Total Tokens: {total_tokens}")

    token_history_service.update_or_insert_token_history(
        user_id=user_id,
        tenant_id=tenant_id,
        token_limit=GEMINI_MAX_GLOBAL_TPM,
        token_consumed=total_tokens,
        usage_date=datetime.now().date(),
    )
    logger.info("Call Analysis : Token history updated successfully")

    inserted_data=request_history_service.insert_request(
        user_id=user_id,
        tenant_id=tenant_id,
        requested_input={
          "callId": call_id,
          "audioFile": file_path,
          "clientPrompt": client_prompt
        },
        generated_response={
          "transcript": transcript_text,
          "summary": summary,
          "sentiment": normalized_sentiment,
          "customerEmotion": normalized_customer_emotion,
          "actionItems": action_items,
        },
        request_date=datetime.now(timezone.utc),
        email_id=None,
        input_token_consumed=input_tokens,
        output_token_consumed=output_tokens,
        total_consumed=total_tokens,
        response_accepted_status=True,
        entity_id=call_id,
        entity_type="call",
        operation="call_analysis",
        status="IN_PROGRESS"
    )
    logger.info("Call Analysis : Request history updated successfully")

    string_action_items = []
    for item in action_items:
      if isinstance(item, dict):
        string_action_items.append(item.get("actionItem", ""))
      else:
        string_action_items.append(str(item))

    response_obj = CallAnalysisResponse(
        callId=call_id,
        transcript=transcript_text,
        summary=summary,
        sentiment=normalized_sentiment,
        customerEmotion=normalized_customer_emotion,
        actionItems=string_action_items,
    )

    return response_obj, action_items, inserted_data

  except Exception as e:
    logger.error(f"Call Analysis : Unexpected error occurred while analyzing call: {e}")
    raise ExternalAIModelException(message=str(e))
