import requests
import os

from loguru import logger
from app.call_analysis.models.call_analysis_models import Task<PERSON><PERSON><PERSON>ieldsResponse
from app.exceptions import TaskFormFieldExtractionError

def get_task_form_fields(token: str) -> TaskFormFieldsResponse:
    # API URL and headers
    logger.info("Fetching task form fields from API")
    base_url = os.getenv("CALL_ANALYSIS_BASE_PATH")
    url = f"http://sd-config/v1/entities/task/fields"
    headers = {
        "Authorization" : f"Bearer {token}",
        "User-Agent": "Call analysis service",
        "Content-Type": "application/json",
    }

    try:
        # Make the API call
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise an exception for HTTP errors
        data = response.json()

        # Initialize results
        priority_values = []
        open_status_id = None

        # Parse the response
        for field in data:
            # Extract priority picklist values
            if field.get("name") == "priority" and field.get("picklist"):
                for value in field["picklist"]["values"]:
                    priority_values.append({
                        "name": value["name"],
                        "id": value["id"]
                    })

            # Extract task status "OPEN"
            if field.get("name") == "status" and field.get("picklist"):
                for value in field["picklist"]["values"]:
                    if value["name"] == "OPEN":
                        open_status_id = {
                            "name": value["name"],
                            "id": value["id"]
                        }

        # Log warnings if fields are missing
        if not priority_values:
            logger.info("Priority fields are missing in the API response.")
        if not open_status_id:
            logger.info("Status field with value 'OPEN' is missing in the API response.")

        logger.info(f"Extracted priority values: {priority_values}")
        logger.info(f"Extracted open status ID: {open_status_id}")

        # Return the extracted values
        return TaskFormFieldsResponse(
            priority_values=priority_values,
            open_status_id=open_status_id
        )

    except requests.exceptions.RequestException as e:
        logger.error(f"API call for task form fields failed : {e}")
        raise TaskFormFieldExtractionError(message=f"API call for task form fields failed : {e}")