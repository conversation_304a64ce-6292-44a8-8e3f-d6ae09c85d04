import requests
from fastapi import HTTPException  # Only if using FastAPI

from loguru import logger


def download_audio_file(audio_url: str, save_path: str) -> str:
    """
    Downloads the audio file from the given URL and saves it to the specified path.
    Args:
        audio_url (str): The URL of the audio file.
        save_path (str): The path where the audio file will be saved.
    Returns:
        str: The path to the saved audio file.
    """
    try:
        response = requests.get(audio_url, stream=True)
        response.raise_for_status()

        with open(save_path, "wb") as audio_file:
            for chunk in response.iter_content(chunk_size=8192):
                audio_file.write(chunk)

        return save_path
    except requests.exceptions.RequestException as e:
        logger.error(f"Error downloading audio file: {e}")
        raise HTTPException(status_code=400, detail="Error downloading audio file.")