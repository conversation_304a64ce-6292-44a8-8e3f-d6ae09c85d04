import os

import requests
from fastapi import HTTPException  # Only if using FastAPI

from loguru import logger


def extract_call_url_and_related_to(response_json):
  try:
    url = response_json.get("callRecording", {}).get("url")
    related_to = response_json.get("relatedTo", [])
    logger.info(f"Extracted call recording URL: {url}")
    logger.info(f"Extracted relatedTo information: {related_to}")
    return url, related_to

  except Exception as e:
    logger.error("Error during URL and relatedTo extraction",
                 extra={"error": str(e)})
    raise HTTPException(status_code=400,
                        detail="Error during URL and relatedTo extraction")


def get_call_recording_url(call_id, token):
  logger.info(f"Getting call recording URL for call_id: {call_id} with token: {token}")
  base_url = os.getenv("CALL_ANALYSIS_BASE_PATH")

  if not base_url:
    logger.error("CALL_ANALYSIS_BASE_PATH environment variable is not set")
    raise ValueError("CALL_ANALYSIS_BASE_PATH environment variable is missing")



  full_url = f"{base_url}/v1/call-logs/{call_id}"

  logger.info(f"Full URL for call recording: {full_url}")

  headers = {
    "Accept": "application/json",
    "Authorization": f"Bearer {token}"
  }

  try:
    logger.info(f"Sending GET request to {full_url} with headers: {headers}")
    response = requests.get(full_url, headers=headers)
    logger.info(
        f"GET {full_url} responded with status {response.status_code}\n"
        f"Headers: {response.headers}\n"
        f"Body: {response.text}"
    )
    response.raise_for_status()
    url, related_to = extract_call_url_and_related_to(response.json())
    logger.info(f"Successfully retrieved call recording URL: {url}")
    logger.info(f"Successfully retrieved relatedTo information: {related_to}")
    return url, related_to

  except requests.exceptions.RequestException as e:
    logger.error(
        f"Error occurred while getting call recording: {str(e)}"
    )
    raise HTTPException(status_code=400,
                        detail="Error occurred while getting call recording.")