import jwt
import datetime
import os

def build_jwt_token(user_id, tenant_id):
    """
    Build a JWT token with all permissions (call, task, customField, user) for internal use.
    """
    secret = os.getenv("SECRET_KEY", "test")  # Use a secure secret in production

    now = datetime.datetime.utcnow()
    expiry = now + datetime.timedelta(minutes=5)  # 5-minute expiry

    # Full permission list
    permissions = [
        {
            "id": 1,
            "name": "call",
            "description": "has access to call resource",
            "action": {
                "read": True, "readAll": True, "write": True,
                "update": True, "delete": True
            }
        },
        {
            "id": 2,
            "name": "task",
            "description": "has access to task resource",
            "action": {
                "read": True, "readAll": True, "write": True,
                "update": True, "delete": True
            }
        },
        {
            "id": 3,
            "name": "customField",
            "description": "has access to field resource",
            "action": {
                "read": True, "readAll": True, "write": True,
                "update": True, "delete": True
            }
        },
        {
            "id": 4,
            "name": "user",
            "description": "has access to user resource",
            "action": {
                "read": True, "readAll": True, "write": True,
                "update": True, "updateAll": True
            }
        }
    ]

    # CoreAccessToken structure
    core_access_token = {
        "accessToken": "internal-token",
        "expiresIn": 5 * 60 * 1000,  # 5 minutes in milliseconds
        "permissions": permissions,
        "expiry": int(expiry.timestamp() * 1000),  # Milliseconds
        "tenantId": str(tenant_id),
        "userId": str(user_id),
        "tokenType": "Bearer",
        "meta": {
            "rate-limit": 5,
            "pid": 2
        }
    }

    payload = {
        "iss": "sell",
        "data": core_access_token,
        "exp": expiry
    }

    token = jwt.encode(payload, secret, algorithm="HS256")
    return token if isinstance(token, str) else token.decode("utf-8")
