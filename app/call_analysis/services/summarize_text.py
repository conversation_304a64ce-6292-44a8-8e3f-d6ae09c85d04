import google.generativeai as genai
import os
from app.exceptions import ExternalAIModelException
from loguru import logger

# Configure the API key
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=GEMINI_API_KEY)


def summarize_text(transcript_text: str,client_prompt: str,current_date_time_utc: str) -> str:
  """
  Summarize the text using OpenAI gpt-4o API.
  Args:
    transcript_text (str): Transcription of the audio file.
  Returns:
    str: The summary of the transcription text.
  """
  try:
    model = genai.GenerativeModel(model_name="gemini-2.0-flash")

    logger.info("Call Analysis : Generating call analysis prompt")
    prompt = f"""You are a helpful assistant that analyzes customer service call transcript. 
    {client_prompt}

    Transcription text : {transcript_text}

    "Provide the following in your analysis:\n"
        "1. A concise summary of the key points discussed, presented as plain text.\n"
        "2. Sentiment analysis of the conversation with sentiment as key and possible values as Positive, Negative, Neutral\n"
        "3. Action items for the sales representative. At max 3 action items should be created. 
        For each action item, include:
            - The action item text.
            - A priority field with one of the following values: HIGH, MEDIUM, LOW.
            - A dueDate field in the format "YYYY-MM-DDTHH:mm:ss.sss+0000". If no due date is mentioned, set it to one day after the {current_date_time_utc}.
        "
        "4. Customer emotion classification - provide **one or more** of the following values that best describe the customer's emotional state. ALWAYS return customerEmotion as a list, even if there is only one emotion. Use the key `customerEmotion` and return it as a list always. Possible values are: Interested, Not Interested, Ready to Buy, Frustrated. "
        "the customer's emotional state and key should be customerEmotion: ["Interested"], ["Not Interested"], ["Ready to Buy"], ["Frustrated"] or multiple values like ["Frustrated", "Interested"]\n\n"

    "Format your response with clear headings for each section and ensure that sentiment and customerEmotion are clearly labeled as follows:\n"
        "summary: [summary as plain text]\n"
        "sentiment: [value]\n"
        "customerEmotion": ["value1", "value2", ...],
        "actionItems: [
            {{
                "actionItem": "[action item text]",
                "priority": "[HIGH/MEDIUM/LOW]",
                "dueDate": "[due date in proper datetime format like YYYY-MM-DDTHH:mm:ss.sss+0000]"
            }},
            ...
        ]\n"
        
    Example response format:
    {{
        "summary":"Plain text summary of the key points discussed in the conversation",
        "sentiment":"positive",
        "customerEmotion":["Frustrated", "Interested"],
        "actionItems":[
          {{
              "actionItem":"Schedule meeting with customer",
              "priority":"HIGH",
              "dueDate":"2025-05-19T14:30:45.123+0000"
          }},
          {{
              "actionItem":"Schedule Call with customer",
              "priority":"MEDIUM",
              "dueDate":"2025-05-18T14:30:45.123+0000"
          }}
        ]
    }}
    """
    logger.info("Call Analysis : Prompt generated successfully")

    # Generate transcription
    response = model.generate_content([prompt])

    logger.info("------------------- Gemini call analysis response ----------------")
    logger.info(f"call analysis/summary response : {response}")

    return response

  except Exception as e:
    logger.error(f"Error summarizing audio file: {e}")
    raise ExternalAIModelException(message="Error summarizing audio file.")