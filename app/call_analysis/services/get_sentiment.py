from openai import OpenAI
import os
from app.exceptions import ExternalAIModelException
from loguru import logger

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def get_sentiment(text : str):
  try:
    response = client.chat.completions.create(
      model="gpt-4o",
      messages=[
        {
          "role": "system",
          "content": (
            "You are an AI assistant that classifies the overall sentiment of a customer service call / sales call with kylas customers / leads(which can be future customers). "
            "Based on the conversion, you should derive sentiment from kylas perspective, if there is customer then consider whether customer is happy with the kylas CRM. If it's a lead consider his probability of buying kylas CRM "
            "The possible sentiment values are: Positive, Negative, Neutral. "
            "Be concise and return only the sentiment category."
          )
        },
        {
          "role": "user",
          "content": f"Analyze the sentiment of this call :\n\n{text}\n\nReturn only the sentiment category."
        }
      ],
      temperature=0.3
    )

    sentiment = response.choices[0].message.content.strip()
    return sentiment
  except Exception as e:
    logger.error(f"Error getting sentiment: {e}")
    raise ExternalAIModelException(message="Error getting sentiment.")