import json
import aio_pika
import os
from loguru import logger
from app.call_analysis.models.call_analysis_models import TaskFormFieldsResponse
from app.exceptions import TaskCreationError, RabbitMQPublishException

# Get RabbitMQ URL and exchange from environment variables
RABBITMQ_URL = os.getenv("RABBITMQ_URL")
AI_EXCHANGE = "ex.ai"

async def create_task(token: str, user_id: int, tenant_id: int, assigned_to: int,
    task_form_fields_response: TaskFormFieldsResponse, action_item: str,
    priority: str, due_date: str, related_to: list):
  """
  Create a task using the call analysis information and task form fields by publishing an event.
  Args:
      tenant_id:
      user_id:
      token (str): Authorization token.
      assigned_to (int): ID of the user to whom the task is assigned.
      task_form_fields_response (TaskFormFieldsResponse): Task form fields response containing priority and status IDs.
      action_item (str): The name of the task (action item).
      priority (str): Priority of the task (<PERSON>IG<PERSON>, MEDIUM, LOW).
      due_date (str): Due date for the task in ISO 8601 format.
      related_to (list): List of related entities.
  Returns:
      bool: True if event was published successfully.
  """

  try:
    logger.info(
      f"Publishing task creation event with action_item: {action_item}, priority: {priority}, due_date: {due_date}, related_to: {related_to}, assigned_to: {assigned_to}")
    logger.info(
      f"Create Task form fields response: {task_form_fields_response}")

    if not RABBITMQ_URL:
      logger.error("RABBITMQ_URL variable is not set")
      raise ValueError("RABBITMQ_URL variable is missing")

    # Initialize priority_id with a default value
    priority_id = None

    # Extract priority ID from task_form_fields_response
    for priority_value in task_form_fields_response.priority_values:
      logger.info(f"Priority value: {priority_value}")
      if priority_value.name.upper() == priority.upper():
        priority_id = priority_value.id
        break

    if not priority_id:
      logger.error(f"Priority '{priority}' not found in task form fields.")
      # Use MEDIUM as default if available
      for priority_value in task_form_fields_response.priority_values:
        if priority_value.name.upper() == "MEDIUM":
          priority_id = priority_value.id
          break

      # If still no priority_id, use the first one
      if not priority_id and task_form_fields_response.priority_values:
        priority_id = task_form_fields_response.priority_values[0].id

    # Get status ID
    status_id = task_form_fields_response.open_status_id.id

    logger.info(
      f"Creating task with priority ID: {priority_id}, status ID: {status_id}")
    logger.info(f"User ID for task assignment: {assigned_to}")
    logger.info(f"Due date for task: {due_date}")

    # Add #Sherpa to the action item
    if action_item is not None:
      action_item = f"{action_item} (#Sherpa)"

    # Prepare the payload
    payload = {
        "userId": user_id,
        "tenantId": tenant_id,
        "dueDate": due_date,
        "assignedTo": assigned_to,
        "status": status_id,
        "name": action_item,
        "priority": priority_id,
        "relation": [
          {
            "targetEntityId": entity["id"],
            "targetEntityType": entity["entity"].upper(),
            "targetEntityName": entity["name"]
          }
          for entity in related_to
        ] if related_to else [],
        "reminder": "ONE_HOUR"
    }
    logger.info(f"Payload for task creation event: {payload}")

    # Connect to RabbitMQ and publish the event
    connection = await aio_pika.connect_robust(RABBITMQ_URL)
    async with connection:
      # Create a channel
      channel = await connection.channel()

      # Declare the exchange
      exchange = await channel.declare_exchange(AI_EXCHANGE, aio_pika.ExchangeType.TOPIC, durable=True)

      # Publish the message
      await exchange.publish(
        aio_pika.Message(
          body=json.dumps(payload).encode(),
          content_type="application/json",
          delivery_mode=aio_pika.DeliveryMode.PERSISTENT
        ),
        routing_key="ai.task.created"
      )

      logger.info(f"Published task creation event to exchange '{AI_EXCHANGE}' with routing key 'ai.task.created' with payload: {payload}")

    return True

  except Exception as e:
    logger.error(f"Error while publishing task creation event: {e}")
    raise RabbitMQPublishException(message=f"Failed to publish task creation event: {e}")
