import os
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>P<PERSON>x<PERSON>, Depends, BackgroundTasks
from fastapi.security import OAuth2P<PERSON><PERSON><PERSON>earer
from fastapi.responses import JSONResponse
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from sqlalchemy.pool import <PERSON><PERSON><PERSON>ool
from datetime import datetime
from dotenv import load_dotenv
from loguru import logger

# Application imports
from app.db.repository.request_history_repository import RequestHistoryService
from app.services.authentication_service.jwt_decoder import \
  validate_user_request
from app.exceptions import (
  AuthenticationError, FileSizeLimitExceededException, TaskCreationError,
  CallAnalysisInProgressException
)
from app.call_analysis.models.call_analysis_models import CallAnalysisRequest
from app.call_analysis.services.get_call_recording_url import \
  get_call_recording_url
from app.call_analysis.services.download_audio_file import download_audio_file
from app.call_analysis.services.analyze_audio import analyze_audio
from app.call_analysis.rabbitmq.publisher import publish_call_analysis_response
from app.call_analysis.services.get_task_form_fields import get_task_form_fields
from app.call_analysis.services.create_task import create_task
from app.db.repository.users_repository import UserService
from app.api.smart_list.v1.smart_list_api import get_user_from_iam

# from app.rate_limit.rate_limiter import rate_limiter as openai_rate_limiter, get_redis as openai_get_redis

# Load environment variables
load_dotenv()
DATABASE_URL = os.getenv("DATABASE_URL")
AUDIO_FILE_SIZE_LIMIT_MB = int(os.getenv("AUDIO_FILE_SIZE_LIMIT_MB", "10"))

# SQLAlchemy setup
engine = create_engine(DATABASE_URL, poolclass=NullPool)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# FastAPI security
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")
router = APIRouter()


def get_db_session():
  db = SessionLocal()
  try:
    yield db
    db.commit()
  except Exception as e:
    db.rollback()
    raise e
  finally:
    db.close()


async def process_call_analysis(request: CallAnalysisRequest, token: str):
  audio_file_path = None
  try:
    tenant_id, user_id, username = validate_user_request(token)
    db_session = next(get_db_session())
    request_history_service = RequestHistoryService(db_session)

    # check if user exists in db
    user_service = UserService(db_session)
    existing_user = user_service.get_user_by_id(user_id)
    logger.info(
        f"Call Analysis : User data extracted from DB in create function - existing_user={existing_user}")

    # if user does not exist in db, fetch user data from IAM
    if not existing_user:
      logger.info(
          f"Call Analysis : Fetching user data from IAM after DB lookup - existing_user={existing_user}")
      iam_user = get_user_from_iam(user_id, token)
      logger.info(
          f"Call Analysis : IAM user data retrieved successfully - iam_user={iam_user}")
      if iam_user:
        # Insert user into local service
        existing_user = user_service.insert_user(
            id=iam_user["id"],
            name=iam_user["name"],
            email=iam_user["email"],
            tenant_id=iam_user["tenant_id"]
        )
        logger.info(
            f"Call Analysis : Inserted new user from IAM - user_id={existing_user.id}")

      else:
        logger.error(
            f"Call Analysis : User not found in IAM - user_id={user_id}")
        raise HTTPException(status_code=400, detail="User not found")

    # Check if call analysis is already in progress
    if request_history_service.exists_status_for_call(request.callId,
                                                      "IN_PROGRESS"):
      logger.warning(f"Call analysis already in progress: {request.callId}")
      return  # Skip processing if already in progress

    # checking rate limit for openai
    # redis = await openai_get_redis()
    # logger.info(f"Call Analysis : Checking open_ai rate limit for tenant {tenant_id}")
    # await openai_rate_limiter(tenant_id, redis)

    logger.info(f"Call analysis started for callId={request.callId}")
    audio_url, related_to = get_call_recording_url(request.callId, token)

    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    audio_file_path = f"/tmp/{request.callId}_{timestamp}.mp3"
    download_audio_file(audio_url, audio_file_path)

    file_size_mb = os.path.getsize(audio_file_path) / (1024 * 1024)
    if file_size_mb > AUDIO_FILE_SIZE_LIMIT_MB:
      raise FileSizeLimitExceededException(
          f"Audio file size exceeds {AUDIO_FILE_SIZE_LIMIT_MB} MB"
      )

    client_prompt = """
            Please transcribe the following audio clip of a customer success call between a Kylas representative and a customer.
            Transcribe in the same language as the audio clip.
        """

    try:
      response, action_item_details, inserted_data = analyze_audio(
          file_path=audio_file_path,
          db_session=db_session,
          user_id=user_id,
          tenant_id=tenant_id,
          call_id=request.callId,
          client_prompt=client_prompt
      )

      await publish_call_analysis_response(response, {}, user_id=user_id)
      task_form_fields_response = get_task_form_fields(token)

      for action_item_detail in action_item_details:
        try:
          task_name = action_item_detail.get("actionItem")
          task_priority = action_item_detail.get("priority", "MEDIUM")
          task_due_date = action_item_detail.get("dueDate")

          await create_task(
              token=token,
              user_id=user_id,
              tenant_id=tenant_id,
              assigned_to=user_id,
              task_form_fields_response=task_form_fields_response,
              action_item=task_name,
              priority=task_priority,
              due_date=task_due_date,
              related_to=related_to,
          )
        except TaskCreationError as e:
          logger.warning(f"Task creation failed: {e}")

      # Update the request status to COMPLETED after all tasks are created
      request_history_service.update_request(
          request_id=inserted_data.id, status="COMPLETED"
      )

    except CallAnalysisInProgressException as e:
      logger.warning(f"Call analysis already in progress: {request.callId}")
      # Just log the error and return, no need to update status
      return

  except Exception as e:
    logger.exception(f"Unhandled exception in call analysis: {str(e)}")
  finally:
    if audio_file_path and os.path.exists(audio_file_path):
      os.remove(audio_file_path)
      logger.info(f"Cleaned up audio file: {audio_file_path}")


@router.post("/call-analysis", status_code=202)
async def analyze_call(
    request: CallAnalysisRequest,
    background_tasks: BackgroundTasks,
    token: str = Depends(oauth2_scheme),
) -> JSONResponse:
  try:
    # Add the processing task to background tasks immediately
    background_tasks.add_task(process_call_analysis, request, token)

    # Return response immediately
    return JSONResponse(
        status_code=202,
        content={"message": "Call analysis started"}
    )

  except AuthenticationError:
    logger.error("Authentication failed")
    raise HTTPException(status_code=401, detail="Invalid token")

  except CallAnalysisInProgressException as e:
    logger.warning(
        f"Call analysis already in progress for call ID: {request.callId}")
    return JSONResponse(
        status_code=409,  # Conflict status code
        content={"error_code": e.error_code, "detail": e.message}
    )

  except Exception as e:
    logger.exception("Error starting call analysis")
    raise HTTPException(status_code=500, detail="Internal Server Error")