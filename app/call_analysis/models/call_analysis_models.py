from pydantic import BaseModel
from typing import List, Optional, Union


# Define the request body model
class CallAnalysisRequest(BaseModel):
    callId: int

# Define the response model
class CallAnalysisResponse(BaseModel):
    callId: int
    transcript: str
    summary: str
    sentiment: str
    customerEmotion: Union[str, List[str]] = []
    actionItems: list[str] = []

class PriorityValue(BaseModel):
    name: str
    id: int

class CallAnalysisAcceptedResponse(BaseModel):
    message: str
    callId: int

class OpenStatus(BaseModel):
    name: str
    id: int

class TaskFormFieldsResponse(BaseModel):
    priority_values: List[PriorityValue]
    open_status_id: Optional[OpenStatus]