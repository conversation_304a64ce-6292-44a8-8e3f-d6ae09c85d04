# from fastapi.testclient import TestClient
# from app.api.v1.email_api_endpoint import app
#
# client = TestClient(app)
#
#
# def test_generate_email_success(client): ## give -> when -> then
#     request_data = {
#         "previous_emails": ["Hello, how are you?", "I am fine, thank you!"],
#         "system_prompt": "Generate a professional response."
#     }
#     response = client.post("/generate-email", json=request_data)
#     assert response.status_code == 200
#     assert response.json() == {
#         "email_response": "Dear User, Thank you for your message. Here's a professional response."
#     }
#
#
#
# def test_generate_email_missing_system_prompt(client):
#     request_data = {
#         "previous_emails": ["Hello, how are you?", "I am fine, thank you!"]
#     }
#     response = client.post("/generate-email", json=request_data)
#     assert response.status_code == 200
#     assert "email_response" in response.json()
#
#
#
# def test_generate_email_empty_previous_emails(client):
#     request_data = {
#         "previous_emails": [],
#         "system_prompt": "Generate a professional response."
#     }
#     response = client.post("/generate-email", json=request_data)
#     assert response.status_code == 400  # Expecting a validation error for empty input
#     assert response.json() == {"detail": "previous_emails cannot be empty."}
#
#
#
# def test_generate_email_invalid_json(client):
#     request_data = {
#         "invalid_key": "Invalid data"
#     }
#     response = client.post("/generate-email", json=request_data)
#     assert response.status_code == 422  # Unprocessable Entity
#
#
#
# def test_generate_email_too_many_emails(client):
#     request_data = {
#         "previous_emails": ["Email" * 1000],  # Excessively large input
#         "system_prompt": "Generate a response."
#     }
#     response = client.post("/generate-email", json=request_data)
#     assert response.status_code == 400  # Assuming validation enforces limits
#     assert response.json() == {"detail": "Too many previous emails."}
