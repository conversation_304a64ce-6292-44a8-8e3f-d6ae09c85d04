import pytest
from unittest.mock import MagicMock
from app.db.models.user_model import User
from app.db.repository.users_repository import UserService
from app.db.models.user_model import User

@pytest.fixture
def mock_session():
    session = MagicMock()
    return session

def test_insert_user(mock_session):
    user_service = UserService(mock_session)
    
    id = 1
    name = "<PERSON>"
    email = "<EMAIL>"
    tenant_id = 123
    
    new_user = User(id=id, name=name, email=email, tenant_id=tenant_id)
    mock_session.add.return_value = None
    mock_session.commit.return_value = None
    mock_session.query().filter().first.return_value = new_user
    
    inserted_user = user_service.insert_user(id, name, email, tenant_id)
    
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    assert inserted_user.name == name
    assert inserted_user.email == email
    assert inserted_user.tenant_id == tenant_id

def test_get_user_by_id(mock_session):
    user_service = UserService(mock_session)
    
    id = 2
    name = "<PERSON>"
    email = "<EMAIL>"
    tenant_id = 456
    
    mock_user = User(id=id, name=name, email=email, tenant_id=tenant_id)
    mock_session.query().filter().first.return_value = mock_user
    
    fetched_user = user_service.get_user_by_id(id)
    
    assert fetched_user is not None
    assert fetched_user.id == id
    assert fetched_user.name == name
    assert fetched_user.email == email
    assert fetched_user.tenant_id == tenant_id

def test_get_user_by_non_existent_id(mock_session):
    user_service = UserService(mock_session)
    
    mock_session.query().filter().first.return_value = None
    
    non_existent_user = user_service.get_user_by_id(9999)
    
    assert non_existent_user is None
