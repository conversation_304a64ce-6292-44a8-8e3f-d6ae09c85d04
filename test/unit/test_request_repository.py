import unittest
from unittest.mock import MagicMock
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from app.db.repository.request_history_repository import RequestHistoryService
from app.exceptions import ResourceNotFoundException


class TestRequestHistoryService(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.service = RequestHistoryService(session=self.mock_session)

    def test_insert_request_failure(self):
        """Test failure of insertion due to SQLAlchemyError."""
        self.mock_session.commit.side_effect = SQLAlchemyError("Insertion failed")
        with self.assertRaises(Exception) as context:
            self.service.insert_request(
                user_id=1,
                tenant_id=123,
                requested_input={"data": "test input"},
                generated_response={"data": "test response"},
                request_date="2025-01-01",
                email_id=999,
                input_token_consumed=10,
                output_token_consumed=5,
                total_consumed=15,
                response_accepted_status=True,
                entity_id=1,
                entity_type="test_entity",
                operation="test_operation"
            )
        self.mock_session.rollback.assert_called_once()
        self.assertIn("Error inserting request", str(context.exception))

    def test_update_response_status_success(self):
        """Test updating response status successfully."""
        mock_request = MagicMock()
        self.mock_session.query().filter().first.return_value = mock_request
        
        result = self.service.update_response_status(request_id=1, status=False)
        
        self.assertEqual(result.response_accepted_status, False)
        self.mock_session.commit.assert_called_once()

    def test_update_response_status_not_found(self):
        """Test updating response status when request is not found."""
        self.mock_session.query().filter().first.return_value = None
        
        with self.assertRaises(ResourceNotFoundException):
            self.service.update_response_status(request_id=1, status=False)
        self.mock_session.commit.assert_not_called()

    def test_update_response_status_failure(self):
        """Test failure of updating response status due to SQLAlchemyError."""
        self.mock_session.query().filter().first.side_effect = SQLAlchemyError("Update failed")
        
        with self.assertRaises(Exception):
            self.service.update_response_status(request_id=1, status=True)
        self.mock_session.rollback.assert_called_once()

    def test_get_request_data_by_id_success(self):
        """Test fetching request data by ID successfully."""
        mock_request = MagicMock(
            id=1,
            user_id=1,
            tenant_id=123,
            requested_input={"data": "test input"},
            generated_response={"data": "test response"},
            request_date="2025-01-01",
            email_id=999,
            input_token_consumed=10,
            output_token_consumed=5,
            total_consumed=15,
            response_accepted_status=True,
            entity_id=1,
            entity_type="test_entity"
        )
        self.mock_session.query().filter().first.return_value = mock_request
        
        result = self.service.get_request_data_by_id(request_id=1)
        
        self.assertEqual(result["id"], mock_request.id)
        self.assertEqual(result["user_id"], mock_request.user_id)

    def test_get_request_data_by_id_not_found(self):
        """Test fetching request data when request is not found."""
        self.mock_session.query().filter().first.return_value = None
        
        with self.assertRaises(HTTPException) as context:
            self.service.get_request_data_by_id(request_id=1)
        self.assertEqual(context.exception.status_code, 400)
        self.assertIn("An unexpected error occurred", context.exception.detail)

    def test_get_request_data_by_id_failure(self):
        """Test failure of fetching request data due to SQLAlchemyError."""
        self.mock_session.query().filter().first.side_effect = SQLAlchemyError("Fetch failed")
        
        with self.assertRaises(HTTPException) as context:
            self.service.get_request_data_by_id(request_id=1)
        self.assertEqual(context.exception.status_code, 400)
        self.assertIn("An unexpected error occurred", context.exception.detail)

if __name__ == "__main__":
    unittest.main()
