import sys
from loguru import logger
import json

# Define Log Format (JSON structured logging)
log_format = (
    '{ "time": "{time:YYYY-MM-DD HH:mm:ss}", '
    '"level": "{level}", '
    '"message": "{message}", '
    '"module": "{module}", '
    '"function": "{function}", '
    '"line": "{line}" }'
)

# Remove default loguru logger
logger.remove()

# Add a structured JSON logger
logger.add(
    sys.stdout,
    format=log_format,
    level="INFO",
    serialize=True  # Converts logs into proper JSON format
)

# If you need to log to a file
logger.add("application.log", rotation="10 MB", format=log_format)

